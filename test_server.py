#!/usr/bin/env python3
"""Test script for the Augment Agent MCP server."""

import asyncio
import json
import subprocess
import sys
from pathlib import Path


async def test_mcp_server():
    """Test the MCP server by running it and checking tool listing."""
    
    print("Testing Augment Agent MCP Server...")
    
    # Test 1: Check if server can start and list tools
    try:
        # Start the server process
        process = subprocess.Popen(
            [sys.executable, "-m", "augment_agent_mcp.server"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send initialize request
        initialize_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        # Send the request
        request_str = json.dumps(initialize_request) + "\n"
        process.stdin.write(request_str)
        process.stdin.flush()
        
        # Read response
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line)
            print(f"✓ Server initialized successfully")
            print(f"  Server info: {response.get('result', {}).get('serverInfo', {})}")
        else:
            print("✗ No response from server")
            return False
        
        # Send tools/list request
        list_tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        request_str = json.dumps(list_tools_request) + "\n"
        process.stdin.write(request_str)
        process.stdin.flush()
        
        # Read response
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line)
            tools = response.get('result', {}).get('tools', [])
            print(f"✓ Found {len(tools)} tools:")
            for tool in tools:
                print(f"  - {tool['name']}: {tool.get('description', 'No description')}")
        else:
            print("✗ No tools list response")
            return False
        
        # Terminate the process
        process.terminate()
        process.wait(timeout=5)
        
        print("✓ Server test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Server test failed: {e}")
        if 'process' in locals():
            process.terminate()
        return False


def test_file_operations():
    """Test file operation tools."""
    print("\nTesting file operations...")
    
    # Create a test file
    test_file = Path("test_file.txt")
    test_content = "Hello, MCP World!\nThis is a test file."
    
    try:
        # Test save_file (simulated)
        test_file.write_text(test_content)
        print("✓ File creation test passed")
        
        # Test view_file (simulated)
        content = test_file.read_text()
        if content == test_content:
            print("✓ File reading test passed")
        else:
            print("✗ File reading test failed")
            return False
        
        # Test file removal
        test_file.unlink()
        if not test_file.exists():
            print("✓ File removal test passed")
        else:
            print("✗ File removal test failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ File operations test failed: {e}")
        # Cleanup
        if test_file.exists():
            test_file.unlink()
        return False


def main():
    """Run all tests."""
    print("=" * 50)
    print("Augment Agent MCP Server Test Suite")
    print("=" * 50)
    
    # Test file operations
    file_test_passed = test_file_operations()
    
    # Test MCP server
    server_test_passed = asyncio.run(test_mcp_server())
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"File Operations: {'PASS' if file_test_passed else 'FAIL'}")
    print(f"MCP Server: {'PASS' if server_test_passed else 'FAIL'}")
    
    if file_test_passed and server_test_passed:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
