# Augment Agent MCP Server Usage Guide

This guide shows you how to use the Augment Agent MCP Server with various MCP clients.

## Quick Start

### 1. Installation

```bash
# Clone or download the project
cd /path/to/augment-agent-mcp-server

# Install the package
pip install -e .

# Verify installation
augment-agent-mcp --help
```

### 2. Test the Server

```bash
# Run the simple test
python simple_test.py

# Expected output:
# ✓ Test passed! Tools are properly registered.
```

## Using with <PERSON>

### 1. Configure <PERSON>

Add the following to your Claude <PERSON> configuration file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "augment-agent": {
      "command": "augment-agent-mcp",
      "args": [],
      "env": {
        "AUGMENT_WORKSPACE": "/path/to/your/workspace"
      }
    }
  }
}
```

### 2. Restart Claude <PERSON>

After saving the configuration, restart <PERSON> completely.

### 3. Verify Connection

Look for the tools icon in <PERSON>. You should see 16 available tools:

- **File Operations**: view_file, save_file, edit_file, remove_files
- **Process Management**: launch_process, read_process, write_process, kill_process, list_processes
- **Web Tools**: web_search, web_fetch, open_browser
- **Development**: get_diagnostics, read_terminal, codebase_search
- **Memory**: remember

## Available Tools

### File Operations

#### view_file
View file or directory contents with optional line range.

**Example**: "Show me the contents of server.py"

#### save_file
Create new files with specified content.

**Example**: "Create a new Python file called hello.py with a simple hello world function"

#### edit_file
Edit existing files using precise string replacement.

**Example**: "In the server.py file, replace the function on lines 50-60 with an improved version"

#### remove_files
Safely delete files.

**Example**: "Delete the temporary files test1.txt and test2.txt"

### Process Management

#### launch_process
Start new processes with optional waiting.

**Example**: "Run 'npm install' in the current directory and wait for completion"

#### read_process / write_process / kill_process
Manage running processes.

**Example**: "Start a development server in the background, then check its output"

### Web Tools

#### web_search
Search the web for information (placeholder - needs API integration).

#### web_fetch
Fetch content from web pages.

**Example**: "Fetch the content from https://example.com"

#### open_browser
Open URLs in the default browser.

**Example**: "Open the project documentation in my browser"

### Development Tools

#### get_diagnostics
Get IDE diagnostics and issues (placeholder - needs IDE integration).

#### read_terminal
Read terminal output (placeholder - needs terminal integration).

#### codebase_search
Search through codebase (placeholder - needs Augment context engine integration).

### Memory

#### remember
Store information for future reference.

**Example**: "Remember that this project uses FastMCP for the MCP server implementation"

## Example Conversations

### File Management
```
User: "Show me the structure of this project"
Assistant: I'll use the view_file tool to explore the project structure...

User: "Create a new configuration file for the database settings"
Assistant: I'll create a new file with database configuration...
```

### Development Workflow
```
User: "Run the tests and show me the results"
Assistant: I'll launch the test process and monitor the output...

User: "If there are any failures, help me fix them"
Assistant: I'll analyze the test output and suggest fixes...
```

### Code Analysis
```
User: "Find all the TODO comments in the codebase"
Assistant: I'll search through the codebase for TODO comments...

User: "Remember to update the documentation before the next release"
Assistant: I'll store that reminder for future reference...
```

## Troubleshooting

### Server Not Appearing in Claude Desktop

1. Check the configuration file syntax
2. Ensure the path to `augment-agent-mcp` is correct
3. Restart Claude Desktop completely
4. Check Claude Desktop logs: `~/Library/Logs/Claude/mcp*.log`

### Tools Not Working

1. Verify the server is running: `augment-agent-mcp`
2. Check for error messages in the logs
3. Ensure proper permissions for file operations
4. Test with simple operations first

### Performance Issues

1. Some tools (like process management) may be slower
2. File operations are optimized for typical development workflows
3. Web tools may timeout on slow connections

## Security Considerations

This server provides powerful system access. Use only in trusted environments:

- File operations can read/write/delete files
- Process management can execute system commands
- Web tools can access external URLs
- Always review tool calls before approval in Claude Desktop

## Extending the Server

To add new tools:

1. Add a new function with the `@mcp.tool()` decorator
2. Include proper type hints and documentation
3. Handle errors gracefully
4. Test the new tool thoroughly

Example:
```python
@mcp.tool()
async def my_new_tool(param: str) -> str:
    """Description of what this tool does.
    
    Args:
        param: Description of the parameter
    """
    try:
        # Implementation here
        return f"Result: {param}"
    except Exception as e:
        return f"Error: {str(e)}"
```
