# 🚀 如何在新项目中使用 MCP 设置脚本

## 📋 四种使用方法

### 方法 1: 直接使用绝对路径 (立即可用)

```bash
# 1. 进入您的新项目目录
cd /path/to/your/new/project

# 2. 运行设置脚本
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py
```

**实际示例**：
```bash
# 创建新项目
mkdir ~/my-awesome-project
cd ~/my-awesome-project

# 设置 MCP 工具
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py

# 在 VS Code 中打开
code .
```

### 方法 2: 指定项目路径 (从任何位置运行)

```bash
# 从任何位置运行，指定目标项目路径
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py /path/to/project
```

**实际示例**：
```bash
# 为多个项目批量设置
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py ~/project1
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py ~/project2
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py ~/project3
```

### 方法 3: 使用全局命令 (需要重启终端)

我已经为您创建了全局命令，重启终端后可以使用：

```bash
# 重启终端或运行
source ~/.zshrc

# 然后在任何项目中使用
cd /path/to/your/project
setup-mcp

# 或指定路径
setup-mcp /path/to/project
```

### 方法 4: 使用别名 (需要重启终端)

```bash
# 重启终端或运行
source ~/.zshrc

# 然后使用别名
cd /path/to/your/project
setup-mcp

# 或指定路径
setup-mcp /path/to/project
```

## 🎯 推荐工作流

### 对于新项目

```bash
# 1. 创建项目目录
mkdir ~/my-new-project
cd ~/my-new-project

# 2. 初始化项目 (如果需要)
git init
npm init -y  # 或其他初始化命令

# 3. 设置 MCP 工具
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py

# 4. 在 VS Code 中打开
code .

# 5. 在 VS Code 中启动 MCP 服务器
# - 打开 .vscode/mcp.json
# - 点击 "Start" 按钮
# - 等待状态变为 "Running"

# 6. 测试 MCP 工具
# - 打开 Copilot Chat
# - 选择 "Agent" 模式
# - 输入: "查看当前项目文件"
```

### 对于现有项目

```bash
# 1. 进入现有项目
cd /path/to/existing/project

# 2. 设置 MCP 工具
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py

# 3. 重新打开 VS Code (如果已经打开)
code .

# 4. 启动 MCP 服务器并测试
```

## 🔧 脚本功能说明

### 脚本会自动：
- ✅ 创建 `.vscode` 目录
- ✅ 生成正确的 `mcp.json` 配置文件
- ✅ 设置正确的工作区路径
- ✅ 使用标准的 GitHub MCP 格式

### 生成的配置文件：
```json
{
  "inputs": [
    {
      "type": "promptString"
    }
  ],
  "servers": {
    "augment-agent": {
      "command": "augment-agent-mcp",
      "args": [],
      "env": {
        "AUGMENT_WORKSPACE": "/actual/project/path"
      }
    }
  }
}
```

## 📋 使用后的步骤

### 在 VS Code 中：

1. **打开项目**
   ```bash
   code /path/to/your/project
   ```

2. **查看配置文件**
   - 打开 `.vscode/mcp.json`
   - 确认配置正确

3. **启动 MCP 服务器**
   - 在 `mcp.json` 文件顶部点击 **"Start"** 按钮
   - 等待状态显示为 **"Running"**

4. **使用 MCP 工具**
   - 打开 Copilot Chat (Ctrl+Shift+I 或 Cmd+Shift+I)
   - 选择 **"Agent"** 模式
   - 点击工具图标 🔧 查看可用工具
   - 测试命令: "查看当前项目的文件结构"

## 🎯 常见问题

### Q: 脚本运行失败怎么办？
```bash
# 检查 Python 是否可用
python --version

# 检查脚本是否存在
ls -la /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py

# 手动运行并查看错误
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py --help
```

### Q: VS Code 中看不到 "Start" 按钮？
- 确保 VS Code 版本 >= 1.99
- 确保 GitHub Copilot 扩展已安装并登录
- 重新打开 `mcp.json` 文件

### Q: MCP 服务器启动失败？
```bash
# 检查 augment-agent-mcp 是否可用
which augment-agent-mcp

# 手动测试启动
augment-agent-mcp --help
```

### Q: 工具列表中看不到 augment-agent？
- 确认 MCP 服务器状态为 "Running"
- 重启 VS Code
- 检查 Copilot Chat 是否在 "Agent" 模式

## 🚀 快速测试

设置完成后，在 Copilot Chat 中测试这些命令：

```
查看当前项目文件
搜索包含 "function" 的代码
创建一个新的 README.md 文件
运行 ls -la 命令
```

如果这些命令能正常工作，说明 MCP 工具设置成功！🎉
