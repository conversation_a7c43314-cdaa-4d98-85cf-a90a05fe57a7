# 🤖 MCP 工具自动调用示例

## 💡 重要说明

**MCP 工具是 AI 自动选择和调用的**，您只需要用自然语言描述需求即可！

## 🎯 实际使用示例

### 📁 文件操作 - AI 自动选择工具

#### 示例 1: 查看文件
```
用户: "看看这个项目有哪些文件"

AI 自动思考: 用户想查看文件结构 → 选择 view_file 工具
AI 执行: view_file(path=".", type="directory")
AI 回复: "这个项目包含以下文件和文件夹：..."
```

#### 示例 2: 创建文件
```
用户: "帮我创建一个 hello.py 文件，包含一个简单的函数"

AI 自动思考: 用户要创建文件 → 选择 save_file 工具
AI 执行: save_file(path="hello.py", content="def hello():\n    print('Hello, World!')")
AI 回复: "已创建 hello.py 文件，包含 hello 函数"
```

#### 示例 3: 编辑文件
```
用户: "在 server.py 的第 20 行添加错误处理"

AI 自动思考: 用户要编辑文件 → 先用 view_file 查看，再用 edit_file 修改
AI 执行: 
  1. view_file(path="server.py") 
  2. edit_file(path="server.py", ...)
AI 回复: "已在 server.py 第 20 行添加了 try-except 错误处理"
```

### 🔧 进程管理 - AI 自动选择工具

#### 示例 4: 运行命令
```
用户: "运行项目的测试"

AI 自动思考: 用户要执行测试 → 选择 launch_process 工具
AI 执行: launch_process(command="npm test", wait=true)
AI 回复: "测试结果：✅ 所有 15 个测试通过"
```

#### 示例 5: 启动服务
```
用户: "启动开发服务器在后台运行"

AI 自动思考: 用户要后台启动服务 → 选择 launch_process 工具
AI 执行: launch_process(command="npm start", wait=false)
AI 回复: "开发服务器已在后台启动，端口 3000"
```

#### 示例 6: 检查进程
```
用户: "看看现在有什么进程在运行"

AI 自动思考: 用户要查看进程 → 选择 list_processes 工具
AI 执行: list_processes()
AI 回复: "当前运行的进程：1. Node.js 服务器 2. Python 脚本..."
```

### 🌐 Web 工具 - AI 自动选择工具

#### 示例 7: 获取网页内容
```
用户: "获取 GitHub API 的用户信息"

AI 自动思考: 用户要获取网页内容 → 选择 web_fetch 工具
AI 执行: web_fetch(url="https://api.github.com/user")
AI 回复: "获取到用户信息：用户名 xxx，仓库数量 xxx..."
```

#### 示例 8: 搜索信息
```
用户: "搜索关于 FastAPI 的最新信息"

AI 自动思考: 用户要搜索信息 → 选择 web_search 工具
AI 执行: web_search(query="FastAPI latest features 2024")
AI 回复: "找到关于 FastAPI 的最新信息：..."
```

### 🔍 开发工具 - AI 自动选择工具

#### 示例 9: 搜索代码
```
用户: "找找代码里所有的 TODO 注释"

AI 自动思考: 用户要搜索代码 → 选择 codebase_search 工具
AI 执行: codebase_search(query="TODO")
AI 回复: "找到 5 个 TODO 注释：1. auth.py 第 45 行..."
```

#### 示例 10: 获取诊断信息
```
用户: "检查项目有没有错误"

AI 自动思考: 用户要检查错误 → 选择 get_diagnostics 工具
AI 执行: get_diagnostics()
AI 回复: "发现 2 个警告：1. 未使用的变量 2. 缺少类型注解"
```

## 🎯 复杂任务 - AI 自动组合工具

### 示例 11: 完整工作流
```
用户: "帮我创建一个新的 API 端点，然后测试它"

AI 自动思考: 这需要多个步骤 → 组合使用多个工具
AI 执行:
  1. view_file(path="app.py") - 查看现有代码
  2. save_file(path="new_endpoint.py") - 创建新端点
  3. edit_file(path="app.py") - 添加路由
  4. launch_process(command="python test_api.py") - 运行测试
AI 回复: "已创建新的 API 端点 /users，并通过了所有测试"
```

### 示例 12: 调试问题
```
用户: "服务器启动失败了，帮我看看什么问题"

AI 自动思考: 需要调试 → 组合使用多个工具
AI 执行:
  1. read_terminal() - 查看终端输出
  2. view_file(path="server.log") - 查看日志文件
  3. get_diagnostics() - 检查代码问题
  4. web_search(query="Node.js EADDRINUSE error") - 搜索解决方案
AI 回复: "问题是端口 3000 被占用，解决方案：..."
```

## 🚀 如何开始使用

### 1. 确保配置正确
```bash
# 检查当前配置
python ai-tools-manager/tools/verify-config.py

# 如果需要，切换到混合模式
python ai-tools-manager/tools/switch-config.py --mode hybrid
```

### 2. 重启 VS Code
完全退出并重新启动 VS Code

### 3. 启动 MCP 服务器
1. 打开 `.vscode/mcp.json` 文件
2. 点击文件顶部的 **"Start"** 按钮
3. 等待状态显示为 **"Running"**

### 4. 开始对话
1. 打开 Copilot Chat
2. 选择 **"Agent"** 模式
3. 开始自然对话！

## 💬 对话技巧

### ✅ 好的提问方式
```
"查看项目文件结构"
"创建一个配置文件"
"运行测试并显示结果"
"搜索代码中的错误"
"获取这个 API 的数据"
```

### ❌ 不需要指定工具
```
❌ "使用 view_file 工具查看文件"
❌ "调用 launch_process 运行命令"
❌ "用 web_search 搜索信息"
```

## 🎉 总结

**您只需要用自然语言描述需求，AI 会自动：**

1. 🧠 **理解您的意图**
2. 🔧 **选择合适的工具**
3. ⚡ **执行具体操作**
4. 📝 **返回结果和说明**

这就是 MCP 工具的强大之处 - **完全自动化的智能工具调用**！🚀
