# 🎯 MCP 工具在新项目中不显示 - 最终解决方案

## 🔍 问题根源

**发现**: MCP 工具配置是 **项目级别** 的，不是全局的。每个项目都需要自己的 `.vscode/mcp.json` 配置文件。

## 📋 GitHub 官方配置方式

根据 GitHub 官方文档，正确的配置方式是：

### 项目级配置 (推荐)
```
项目根目录/
└── .vscode/
    └── mcp.json  ← 每个项目都需要这个文件
```

### 配置格式
```json
{
  "inputs": [
    {
      "type": "promptString"
    }
  ],
  "servers": {
    "augment-agent": {
      "command": "augment-agent-mcp",
      "args": [],
      "env": {
        "AUGMENT_WORKSPACE": "/path/to/current/project"
      }
    }
  }
}
```

## 🚀 解决方案

### 方法 1: 使用通用设置脚本

我已经创建了一个通用脚本，可以为任何项目快速设置 MCP 工具：

```bash
# 为当前项目设置
cd /path/to/your/new/project
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py

# 为指定项目设置
python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py /path/to/project
```

### 方法 2: 手动复制配置

```bash
# 1. 在新项目中创建 .vscode 目录
mkdir -p /path/to/new/project/.vscode

# 2. 复制配置文件
cp /Users/<USER>/Documents/augment-projects/mcp/.vscode/mcp.json /path/to/new/project/.vscode/

# 3. 编辑工作区路径
# 将 AUGMENT_WORKSPACE 改为新项目的路径
```

### 方法 3: 创建项目模板

```bash
# 创建一个包含 MCP 配置的项目模板
mkdir -p ~/project-template/.vscode
cp /Users/<USER>/Documents/augment-projects/mcp/.vscode/mcp.json ~/project-template/.vscode/

# 以后创建新项目时
cp -r ~/project-template /path/to/new/project
```

## 📋 详细步骤

### 为新项目设置 MCP 工具

1. **运行设置脚本**
   ```bash
   cd /path/to/your/new/project
   python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py
   ```

2. **在 VS Code 中打开项目**
   ```bash
   code /path/to/your/new/project
   ```

3. **启动 MCP 服务器**
   - 打开 `.vscode/mcp.json` 文件
   - 点击文件顶部的 **"Start"** 按钮
   - 等待状态显示为 **"Running"**

4. **使用 MCP 工具**
   - 打开 Copilot Chat
   - 选择 **"Agent"** 模式
   - 点击工具图标 🔧 查看可用工具
   - 测试: "查看当前项目文件"

## 🎯 为什么之前的全局配置不工作

### 原因分析
1. **GitHub 官方方式**: 使用项目级 `.vscode/mcp.json`
2. **我们尝试的方式**: 全局 `settings.json` 配置
3. **结果**: 全局配置不被 Copilot 识别

### 正确理解
- **每个项目** 都需要自己的 MCP 配置
- **工作区路径** 需要指向当前项目
- **服务器启动** 是项目级别的

## 🔧 自动化解决方案

### 创建 VS Code 任务

在项目的 `.vscode/tasks.json` 中添加：

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Setup MCP Tools",
      "type": "shell",
      "command": "python",
      "args": [
        "/Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py",
        "${workspaceFolder}"
      ],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    }
  ]
}
```

### 创建 Shell 别名

在 `~/.zshrc` 或 `~/.bashrc` 中添加：

```bash
alias setup-mcp='python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py'
```

然后在任何项目中运行：
```bash
cd /path/to/project
setup-mcp
```

## 🎉 最终工作流

### 对于新项目
1. **创建或进入项目目录**
2. **运行**: `python /Users/<USER>/Documents/augment-projects/mcp/setup-mcp-for-any-project.py`
3. **在 VS Code 中打开项目**
4. **启动 MCP 服务器** (点击 "Start" 按钮)
5. **开始使用 MCP 工具**

### 对于现有项目
- 只需要运行一次设置脚本
- 配置会保存在项目中
- 团队成员也能使用相同配置

## 📊 配置验证

### 检查配置是否正确
```bash
# 检查文件是否存在
ls -la .vscode/mcp.json

# 检查配置内容
cat .vscode/mcp.json

# 检查工作区路径是否正确
grep "AUGMENT_WORKSPACE" .vscode/mcp.json
```

### 在 VS Code 中验证
1. 打开 `.vscode/mcp.json`
2. 确认有 "Start" 按钮
3. 点击启动，确认状态为 "Running"
4. 在 Copilot Chat 中查看工具列表

## 🎯 总结

**问题**: MCP 工具只在原项目中显示，新项目中不显示
**原因**: MCP 配置是项目级别的，每个项目都需要自己的配置
**解决**: 为每个项目创建 `.vscode/mcp.json` 配置文件
**工具**: 使用我创建的通用设置脚本快速配置

现在您知道了正确的方法，可以在任何项目中快速设置 MCP 工具！🚀
