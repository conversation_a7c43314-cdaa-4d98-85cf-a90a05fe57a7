# 🔧 MCP 工具不显示问题解决指南

## 🎯 问题描述

在新项目中打开 VS Code，发现 MCP 工具没有显示出来。

## 🔍 问题原因

### 主要原因
1. **配置是项目级别的** - 原项目的 `.vscode/mcp.json` 不会自动复制到新项目
2. **MCP 服务器未启动** - 新项目中没有启动 MCP 服务器
3. **全局配置缺失** - VS Code 的全局设置中没有 MCP 配置

## 🚀 解决方案

### 方案 1: 设置全局配置 (推荐)

**一次设置，所有项目可用**

```bash
# 设置全局 MCP 配置
python ai-tools-manager/tools/setup-global-mcp.py --setup

# 检查配置状态
python ai-tools-manager/tools/setup-global-mcp.py --check
```

**优势**:
- ✅ 所有项目自动支持 MCP 工具
- ✅ 无需为每个项目单独配置
- ✅ 工作区路径自动适配

### 方案 2: 为新项目单独配置

**在新项目目录中运行**

```bash
# 切换到新项目目录
cd /path/to/your/new/project

# 设置 MCP 工具
python /path/to/ai-tools-manager/tools/setup-mcp-for-project.py --setup

# 验证设置
python /path/to/ai-tools-manager/tools/setup-mcp-for-project.py --verify
```

## 📋 详细步骤

### 使用全局配置 (推荐)

#### 1. 设置全局配置
```bash
cd /Users/<USER>/Documents/augment-projects/mcp
python ai-tools-manager/tools/setup-global-mcp.py --setup
```

#### 2. 重启 VS Code
完全退出并重新启动 VS Code

#### 3. 打开任意项目
在 VS Code 中打开任何项目

#### 4. 使用 MCP 工具
- 打开 Copilot Chat
- 选择 "Agent" 模式
- 开始使用: "查看当前项目文件"

### 使用项目配置

#### 1. 在新项目中设置
```bash
# 假设新项目在 ~/my-new-project
cd ~/my-new-project

# 运行设置脚本
python /Users/<USER>/Documents/augment-projects/mcp/ai-tools-manager/tools/setup-mcp-for-project.py --setup
```

#### 2. 重启 VS Code
在新项目中重启 VS Code

#### 3. 启动 MCP 服务器
- 查看 `.vscode/mcp.json` 文件
- 点击文件顶部的 "Start" 按钮
- 等待状态显示为 "Running"

#### 4. 使用工具
- 打开 Copilot Chat
- 选择 "Agent" 模式
- 点击工具图标 🔧 查看可用工具

## 🔍 故障排除

### 检查 MCP 服务器状态
```bash
# 检查命令是否可用
which augment-agent-mcp

# 如果不可用，重新安装
cd /Users/<USER>/Documents/augment-projects/mcp
pip install -e .
```

### 检查配置文件

#### 全局配置检查
```bash
python ai-tools-manager/tools/setup-global-mcp.py --check
```

#### 项目配置检查
```bash
# 在项目目录中
ls -la .vscode/
cat .vscode/mcp.json
```

### 检查 VS Code 设置

#### 全局设置位置
```
~/Library/Application Support/Code/User/settings.json
```

#### 应该包含的配置
```json
{
  "chat.mcp.discovery.enabled": true,
  "chat.mcp.servers": {
    "augment-agent": {
      "command": "augment-agent-mcp",
      "args": [],
      "env": {
        "AUGMENT_WORKSPACE": "${workspaceFolder}"
      }
    }
  }
}
```

## 🎯 验证工具是否工作

### 1. 检查工具列表
在 Copilot Chat 中:
- 选择 "Agent" 模式
- 点击工具图标 🔧
- 应该看到 "augment-agent" 和 16 个工具

### 2. 测试工具调用
```
在 Copilot Chat 中输入:
"查看当前目录的文件"
"创建一个测试文件 test.txt"
```

### 3. 运行验证脚本
```bash
cd /Users/<USER>/Documents/augment-projects/mcp
python verify_github_copilot_mcp.py
```

## 🔄 配置对比

### 全局配置 vs 项目配置

| 特性 | 全局配置 | 项目配置 |
|------|----------|----------|
| 设置复杂度 | 简单 | 中等 |
| 适用范围 | 所有项目 | 单个项目 |
| 维护成本 | 低 | 高 |
| 灵活性 | 中等 | 高 |
| 推荐程度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🎉 快速解决

### 最快的解决方案

```bash
# 1. 设置全局配置 (一次性)
cd /Users/<USER>/Documents/augment-projects/mcp
python ai-tools-manager/tools/setup-global-mcp.py --setup

# 2. 重启 VS Code

# 3. 打开任意项目，开始使用
```

### 验证是否成功

```bash
# 检查全局配置
python ai-tools-manager/tools/setup-global-mcp.py --check

# 在 Copilot Chat 中测试
"查看当前项目文件"
```

## 💡 预防措施

### 避免未来出现此问题

1. **使用全局配置** - 一次设置，永久有效
2. **创建项目模板** - 包含 MCP 配置的项目模板
3. **文档化流程** - 记录新项目的设置步骤

### 团队协作

如果是团队项目，建议:
1. 在项目中包含 `.vscode/mcp.json`
2. 在 README 中说明 MCP 工具的使用
3. 提供快速设置脚本

## 🎯 总结

**问题**: 新项目中 MCP 工具不显示
**原因**: 配置是项目级别的，新项目缺少配置
**解决**: 设置全局配置或为新项目单独配置
**推荐**: 使用全局配置，一次设置，所有项目可用

现在您可以在任何项目中使用 MCP 工具了！🚀
