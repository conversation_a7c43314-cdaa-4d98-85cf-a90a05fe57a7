#!/bin/bash

# Augment Agent MCP Server Installation Script

set -e

echo "Installing Augment Agent MCP Server..."

# Check if Python 3.10+ is available
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
required_version="3.10"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "Error: Python 3.10 or higher is required. Found: $python_version"
    exit 1
fi

echo "✓ Python version check passed: $python_version"

# Install the package
echo "Installing package..."
pip install -e .

echo "✓ Package installed successfully"

# Test the installation
echo "Testing installation..."
if command -v augment-agent-mcp &> /dev/null; then
    echo "✓ Command line tool installed successfully"
else
    echo "✗ Command line tool not found in PATH"
    exit 1
fi

# Run basic tests
echo "Running basic tests..."
python test_server.py

echo ""
echo "Installation completed successfully!"
echo ""
echo "To use with <PERSON>:"
echo "1. Copy the configuration from claude_desktop_config_example.json"
echo "2. Add it to your Claude Desktop configuration file:"
echo "   - macOS: ~/Library/Application Support/Claude/claude_desktop_config.json"
echo "   - Windows: %APPDATA%\\Claude\\claude_desktop_config.json"
echo "3. Restart Claude Desktop"
echo ""
echo "Available tools:"
echo "- File operations: view_file, save_file, edit_file, remove_files"
echo "- Process management: launch_process, read_process, write_process, kill_process, list_processes"
echo "- Web tools: web_search, web_fetch, open_browser"
echo "- Development: get_diagnostics, read_terminal, codebase_search"
echo "- Memory: remember"
