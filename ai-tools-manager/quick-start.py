#!/usr/bin/env python3
"""AI 工具管理器快速启动脚本"""

import sys
from pathlib import Path

# 添加工具路径
tools_path = Path(__file__).parent / "tools"
sys.path.insert(0, str(tools_path))

from switch_config import ConfigSwitcher


def main():
    """快速启动主函数"""
    print("🚀 AI 工具管理器")
    print("=" * 30)
    print()
    print("这个工具可以帮您解决 Augment 和 GitHub Copilot 的冲突问题。")
    print()
    print("🎯 推荐配置:")
    print("1. hybrid (混合模式) - Augment编码 + Copilot Chat/MCP工具")
    print("2. copilot-only - 仅使用 GitHub Copilot")
    print("3. augment-only - 仅使用 Augment")
    print()
    
    choice = input("是否要进入配置选择? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', '是']:
        switcher = ConfigSwitcher()
        switcher.interactive_switch()
    else:
        print("👋 您可以随时运行以下命令来切换配置:")
        print("  python ai-tools-manager/tools/switch-config.py")
        print("  python ai-tools-manager/tools/verify-config.py")


if __name__ == "__main__":
    main()
