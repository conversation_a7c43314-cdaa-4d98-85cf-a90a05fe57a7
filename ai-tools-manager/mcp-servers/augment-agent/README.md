# Augment Agent MCP 服务器

## 📝 服务器说明

这是我们创建的 Augment Agent MCP 服务器，提供 16 个强大的工具。

## 🔧 包含的工具

### 文件操作 (4个)
- **view_file**: 查看文件和目录内容
- **save_file**: 创建新文件
- **edit_file**: 编辑现有文件
- **remove_files**: 删除文件

### 进程管理 (5个)
- **launch_process**: 启动进程
- **read_process**: 读取进程输出
- **write_process**: 向进程写入
- **kill_process**: 终止进程
- **list_processes**: 列出进程

### Web 工具 (3个)
- **web_search**: 网络搜索
- **web_fetch**: 获取网页内容
- **open_browser**: 打开浏览器

### 开发工具 (3个)
- **get_diagnostics**: 获取诊断信息
- **read_terminal**: 读取终端输出
- **codebase_search**: 搜索代码库

### 记忆工具 (1个)
- **remember**: 存储信息

## 🚀 使用方法

这个服务器会在使用 `copilot-only` 或 `hybrid` 配置时自动启用。

## 📍 服务器位置

服务器代码位于项目根目录的 `augment_agent_mcp/` 文件夹中。
