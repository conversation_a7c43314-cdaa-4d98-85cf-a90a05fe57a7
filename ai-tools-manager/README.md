# AI 工具管理器

这个文件夹包含了不同 AI 助手的独立配置，您可以根据需要选择使用。

## 📁 文件夹结构

```
ai-tools-manager/
├── README.md                 # 本文件
├── configs/                  # 配置文件
│   ├── augment-only/        # 仅使用 Augment
│   ├── copilot-only/        # 仅使用 Copilot + MCP
│   ├── hybrid/              # 混合模式 (Augment编码 + Copilot Chat/MCP)
│   └── custom/              # 自定义配置
├── tools/                   # 工具脚本
│   ├── switch-config.py     # 配置切换工具
│   ├── backup-config.py     # 配置备份工具
│   └── verify-config.py     # 配置验证工具
└── mcp-servers/             # MCP 服务器
    ├── augment-agent/       # Augment Agent MCP 服务器
    ├── github-server/       # GitHub MCP 服务器
    └── custom-servers/      # 自定义 MCP 服务器
```

## 🚀 快速开始

### 1. 选择配置模式
```bash
cd ai-tools-manager
python tools/switch-config.py
```

### 2. 可用模式
- **augment-only**: 仅使用 Augment，无冲突
- **copilot-only**: 仅使用 GitHub Copilot + MCP 工具
- **hybrid**: Augment 编码 + Copilot Chat/MCP (推荐)
- **custom**: 自定义配置

### 3. 验证配置
```bash
python tools/verify-config.py
```

## 🔧 使用说明

每个配置模式都是完全独立的，可以随时切换而不影响其他配置。

详细使用说明请查看各个配置文件夹中的 README.md 文件。
