# 🎯 AI 助手提示词管理

## 📝 概述

这个文件夹管理不同 AI 助手的工作提示词和指导原则。

## 📁 文件结构

```
prompts/
├── README.md                    # 本文件
├── augment/                     # Augment 提示词
│   ├── coding-guidelines.md     # 编码指导原则
│   ├── project-context.md       # 项目上下文
│   └── user-preferences.md      # 用户偏好
├── copilot/                     # Copilot 提示词
│   ├── chat-instructions.md     # Chat 指导
│   └── mcp-context.md          # MCP 工具上下文
└── shared/                      # 共享提示词
    ├── best-practices.md        # 最佳实践
    └── project-standards.md     # 项目标准
```

## 🔧 使用方法

### 应用 Augment 提示词
```bash
python ai-tools-manager/tools/apply-prompts.py --target augment
```

### 应用 Copilot 提示词  
```bash
python ai-tools-manager/tools/apply-prompts.py --target copilot
```

### 查看当前提示词
```bash
python ai-tools-manager/tools/view-prompts.py
```

## 🎯 提示词类型

### 1. 编码指导
- 代码风格偏好
- 架构原则
- 最佳实践

### 2. 项目上下文
- 项目结构说明
- 技术栈信息
- 业务逻辑

### 3. 用户偏好
- 交互风格
- 详细程度
- 输出格式
