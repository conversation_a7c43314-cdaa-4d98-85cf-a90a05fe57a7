# 🎯 当前项目上下文

## 📋 项目概述

### 项目名称
MCP 工具集成项目

### 项目目标
- 集成 Augment Agent 的 16 个 MCP 工具
- 解决 Augment 和 GitHub Copilot 的冲突问题
- 提供灵活的 AI 助手配置管理系统
- 实现无缝的工具自动调用体验

### 技术栈
- **语言**: Python 3.12+
- **框架**: FastMCP (MCP 服务器框架)
- **工具**: VS Code, GitHub Copilot, Augment
- **协议**: Model Context Protocol (MCP)

## 🏗️ 项目结构

```
/Users/<USER>/Documents/augment-projects/mcp/
├── augment_agent_mcp/           # MCP 服务器实现
│   ├── server.py               # 主服务器文件
│   ├── tools/                  # 16个工具实现
│   └── __init__.py
├── ai-tools-manager/           # 配置管理系统
│   ├── configs/               # 不同模式配置
│   ├── tools/                 # 管理工具脚本
│   ├── prompts/              # 提示词管理
│   └── mcp-servers/          # MCP 服务器管理
├── .vscode/
│   └── mcp.json              # VS Code MCP 配置
└── 各种配置和文档文件
```

## 🔧 核心功能

### MCP 工具 (16个)
1. **文件操作**: view_file, save_file, edit_file, remove_files
2. **进程管理**: launch_process, read_process, write_process, kill_process, list_processes
3. **Web 工具**: web_search, web_fetch, open_browser
4. **开发工具**: get_diagnostics, read_terminal, codebase_search
5. **记忆功能**: remember

### 配置管理
- **hybrid 模式**: Augment编码 + Copilot Chat/MCP (推荐)
- **copilot-only 模式**: 纯 GitHub Copilot + MCP
- **augment-only 模式**: 纯 Augment，无冲突

### 自动化工具
- 配置切换器
- 兼容性检查器
- 更新安全管理器

## 🎯 当前状态

### 已完成功能
- ✅ MCP 服务器完整实现
- ✅ 16个工具全部可用
- ✅ 配置管理系统
- ✅ 冲突解决方案
- ✅ 更新兼容性保障

### 当前配置
- **模式**: hybrid (混合模式)
- **Augment**: 负责代码补全
- **Copilot**: 负责 Chat 和 MCP 工具调用
- **状态**: 无冲突，完全兼容

### 验证状态
```
✅ MCP 服务器: 正常运行
✅ 工具注册: 16个工具全部可用
✅ VS Code 集成: 配置正确
✅ 兼容性: 3/3 检查通过
```

## 🔍 开发重点

### 代码质量
- 所有工具都有完整的错误处理
- 使用 FastMCP 框架确保稳定性
- 遵循 MCP 协议标准
- 完整的日志记录

### 用户体验
- 自然语言交互
- AI 自动工具选择
- 无需手动配置
- 智能错误恢复

### 维护性
- 模块化设计
- 配置文件分离
- 自动备份机制
- 更新兼容性保障

## 📝 使用场景

### 日常开发
- 文件操作和项目管理
- 代码搜索和分析
- 自动化任务执行
- 开发环境管理

### 系统操作
- 进程监控和管理
- 终端命令执行
- 系统诊断
- 日志分析

### 信息获取
- 网络搜索和数据获取
- API 调用和测试
- 文档查询
- 技术资料收集

## 🎯 优化方向

### 性能优化
- 工具调用响应时间
- 内存使用优化
- 并发处理能力
- 缓存机制

### 功能扩展
- 更多专业工具
- 自定义工具支持
- 工作流自动化
- 团队协作功能

### 用户体验
- 更智能的工具选择
- 更好的错误提示
- 个性化配置
- 学习用户习惯

## 🔒 安全考虑

### 权限控制
- 文件操作权限限制
- 进程执行安全检查
- 网络访问控制
- 敏感信息保护

### 数据安全
- 不记录敏感信息
- 安全的临时文件处理
- 加密通信
- 审计日志
