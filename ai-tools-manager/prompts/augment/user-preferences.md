# 🎯 Augment 用户偏好设置

## 💬 交互风格偏好

### 沟通方式
- 使用简洁明了的语言
- 提供具体的代码示例
- 解释技术决策的原因
- 主动提出改进建议
- 使用中文进行交流

### 响应详细程度
- 代码建议要包含注释
- 提供多种解决方案选择
- 说明潜在的风险和注意事项
- 包含相关的最佳实践
- 给出后续步骤建议

### 输出格式偏好
- 代码块使用语法高亮
- 使用清晰的标题和分段
- 重要信息用 emoji 标记
- 提供可执行的命令示例
- 包含相关文档链接

## 🔧 技术偏好

### 编程语言优先级
1. Python (主要用于后端和脚本)
2. TypeScript (前端开发)
3. JavaScript (快速原型)
4. Bash (系统脚本)

### 框架和工具偏好
- **后端**: FastAPI > Flask > Django
- **前端**: React > Vue.js > Angular
- **数据库**: PostgreSQL > MySQL > SQLite
- **部署**: Docker + Kubernetes
- **测试**: pytest (Python), Jest (JavaScript)

### 开发环境
- VS Code 作为主要编辑器
- Git 版本控制
- Docker 本地开发环境
- 自动化测试和部署

## 📋 项目管理偏好

### 代码组织
- 模块化和可重用的代码
- 清晰的文件夹结构
- 统一的命名约定
- 完整的文档和注释

### 版本控制
- 使用语义化版本号
- 清晰的 Git 提交信息
- 功能分支开发模式
- 代码审查流程

### 质量保证
- 自动化测试覆盖
- 代码质量检查工具
- 性能监控和优化
- 安全扫描和审计

## 🎯 学习和改进偏好

### 知识获取
- 优先查看官方文档
- 关注最佳实践和设计模式
- 学习新技术和工具
- 参考开源项目实现

### 技能提升
- 定期重构和优化代码
- 尝试新的开发方法
- 关注性能和可维护性
- 持续集成和部署实践

## 🔍 问题解决偏好

### 调试方法
- 使用调试工具而非 print 语句
- 编写测试用例复现问题
- 查看日志和错误信息
- 逐步缩小问题范围

### 解决方案选择
- 优先选择简单可靠的方案
- 考虑长期维护成本
- 评估性能影响
- 确保向后兼容性

## 🚀 效率提升偏好

### 自动化
- 重复任务自动化
- 使用脚本简化操作
- 配置开发环境模板
- 自动化测试和部署

### 工具使用
- 快捷键和命令行工具
- 代码片段和模板
- 自动补全和智能提示
- 集成开发环境优化

## 📊 代码质量标准

### 可读性
- 代码自文档化
- 合理的函数和类大小
- 清晰的变量和函数命名
- 适当的注释和文档

### 可维护性
- 低耦合高内聚
- 单一职责原则
- 开闭原则
- 依赖倒置原则

### 可测试性
- 纯函数优于有副作用的函数
- 依赖注入便于测试
- 模块化设计
- 清晰的接口定义
