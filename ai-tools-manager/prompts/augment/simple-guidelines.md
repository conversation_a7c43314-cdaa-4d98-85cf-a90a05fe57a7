# Augment 工作指导原则

## 代码风格
- Python: 使用 4 空格缩进，遵循 PEP 8，添加类型注解
- JavaScript/TypeScript: 使用 2 空格缩进，优先使用 const/let，添加类型注解
- 变量和函数名要有描述性，添加必要注释
- 函数保持简洁，单一职责原则

## 技术偏好
- 后端: FastAPI > Flask，使用 pydantic 数据验证
- 前端: React + TypeScript，使用现代 ES6+ 语法
- 数据库: PostgreSQL 优先，合理使用索引
- 测试: pytest (Python), Jest (JavaScript)，要求测试覆盖

## 项目管理
- 清晰的文件夹结构，模块化设计
- Git 提交信息要清晰，使用语义化版本
- 完整的 README 和 API 文档
- 错误处理要完整，日志记录详细

## 交互偏好
- 使用中文交流，提供具体代码示例
- 解释技术决策原因，主动提出改进建议
- 代码要有注释，包含最佳实践说明
- 提供多种解决方案，说明优缺点

## 当前项目上下文
这是一个 MCP 工具集成项目，包含 16 个工具：文件操作、进程管理、Web 工具、开发工具和记忆功能。使用 Python + FastMCP 框架，集成 VS Code + GitHub Copilot。当前使用混合模式：Augment 负责代码补全，Copilot 负责 Chat 和 MCP 工具调用。
