# 🎯 Augment 编码指导原则

## 📋 代码风格偏好

### Python 代码风格
- 使用 4 空格缩进
- 遵循 PEP 8 标准
- 使用类型注解
- 函数和类要有详细的文档字符串
- 优先使用 f-string 进行字符串格式化

### JavaScript/TypeScript 风格
- 使用 2 空格缩进
- 优先使用 const，其次 let，避免 var
- 使用箭头函数
- 添加 TypeScript 类型注解
- 使用模板字符串

### 通用原则
- 变量和函数名使用描述性命名
- 避免过长的函数，单一职责原则
- 添加适当的注释解释复杂逻辑
- 错误处理要完整
- 代码要易于测试

## 🏗️ 架构原则

### 项目结构
- 清晰的文件夹组织
- 分离关注点
- 模块化设计
- 配置文件集中管理

### 设计模式
- 优先使用组合而非继承
- 依赖注入
- 工厂模式用于对象创建
- 观察者模式用于事件处理

### 性能考虑
- 避免不必要的计算
- 合理使用缓存
- 异步处理长时间操作
- 内存使用优化

## 🔧 工具和库偏好

### Python 生态
- 使用 FastAPI 构建 API
- pytest 进行测试
- pydantic 进行数据验证
- black 进行代码格式化

### JavaScript 生态
- 使用 React 或 Vue.js
- TypeScript 优于 JavaScript
- Jest 进行测试
- ESLint + Prettier 代码质量

### 开发工具
- Git 版本控制，清晰的提交信息
- Docker 容器化部署
- CI/CD 自动化流程
- 代码审查流程

## 📝 文档要求

### 代码文档
- 每个函数都要有文档字符串
- 复杂算法要有注释说明
- API 接口要有完整文档
- 配置选项要有说明

### 项目文档
- README.md 包含项目介绍和使用方法
- CHANGELOG.md 记录版本变更
- 部署文档详细清晰
- 故障排除指南

## 🧪 测试策略

### 测试覆盖
- 单元测试覆盖核心逻辑
- 集成测试验证模块交互
- 端到端测试验证用户流程
- 性能测试确保响应时间

### 测试质量
- 测试用例要有意义
- 测试数据要真实
- 边界条件要覆盖
- 错误情况要测试

## 🔒 安全考虑

### 数据安全
- 敏感信息不硬编码
- 使用环境变量管理配置
- 输入验证和清理
- 适当的权限控制

### 代码安全
- 依赖库定期更新
- 安全扫描工具使用
- 代码审查关注安全
- 错误信息不泄露敏感信息
