# 混合模式配置 (推荐)

## 📝 配置说明

这个配置结合了两个平台的优势：使用 Augment 进行编码，使用 Copilot 进行对话和工具操作。

## ✅ 启用功能
- Augment 代码补全 (编码时)
- GitHub Copilot Chat (对话)
- MCP 工具 (16个 Augment Agent 工具)
- Agent 模式

## ❌ 禁用功能
- GitHub Copilot 代码补全 (避免冲突)

## 🎯 适用场景
- 想要最佳编码体验的用户
- 需要强大对话和工具功能
- 希望避免 AI 助手冲突
- 大多数开发场景 (推荐)

## 🚀 使用方法
```bash
cd ai-tools-manager
python tools/switch-config.py --mode hybrid
```

## 💡 使用技巧
- **编码时**: 享受 Augment 的智能补全
- **需要对话**: 打开 Copilot Chat，选择 Agent 模式
- **系统操作**: 使用 MCP 工具进行文件操作、进程管理等
