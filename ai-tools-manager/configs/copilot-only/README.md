# Copilot 专用配置

## 📝 配置说明

这个配置完全使用 GitHub Copilot，包括代码补全、Chat 和 MCP 工具。

## ✅ 启用功能
- GitHub Copilot 代码补全
- GitHub Copilot Chat
- MCP 工具 (16个 Augment Agent 工具)
- Agent 模式

## ❌ 禁用功能
- Augment 代码补全 (需要手动禁用扩展)

## 🎯 适用场景
- 专注使用 GitHub Copilot 的用户
- 需要完整 MCP 工具集的项目
- 喜欢 Copilot 代码风格的开发者

## 🚀 使用方法
```bash
cd ai-tools-manager
python tools/switch-config.py --mode copilot-only
```

## 📋 手动步骤
切换到此模式后，建议在 VS Code 中手动禁用 Augment 扩展以避免冲突。
