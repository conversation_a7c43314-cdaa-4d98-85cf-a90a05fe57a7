#!/usr/bin/env python3
"""快速设置 AI 助手提示词"""

import json
import sys
from pathlib import Path


def apply_simple_augment_prompts():
    """应用简化的 Augment 提示词"""
    
    # 简化的提示词内容
    simple_prompts = """# Augment 工作指导原则

## 代码风格
- Python: 使用 4 空格缩进，遵循 PEP 8，添加类型注解
- JavaScript/TypeScript: 使用 2 空格缩进，优先使用 const/let，添加类型注解
- 变量和函数名要有描述性，添加必要注释
- 函数保持简洁，单一职责原则

## 技术偏好
- 后端: FastAPI > Flask，使用 pydantic 数据验证
- 前端: React + TypeScript，使用现代 ES6+ 语法
- 数据库: PostgreSQL 优先，合理使用索引
- 测试: pytest (Python), Jest (JavaScript)，要求测试覆盖

## 项目管理
- 清晰的文件夹结构，模块化设计
- Git 提交信息要清晰，使用语义化版本
- 完整的 README 和 API 文档
- 错误处理要完整，日志记录详细

## 交互偏好
- 使用中文交流，提供具体代码示例
- 解释技术决策原因，主动提出改进建议
- 代码要有注释，包含最佳实践说明
- 提供多种解决方案，说明优缺点

## 当前项目上下文
这是一个 MCP 工具集成项目，包含 16 个工具：文件操作、进程管理、Web 工具、开发工具和记忆功能。使用 Python + FastMCP 框架，集成 VS Code + GitHub Copilot。当前使用混合模式：Augment 负责代码补全，Copilot 负责 Chat 和 MCP 工具调用。"""

    vscode_settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
    
    try:
        # 读取当前设置
        current_settings = {}
        if vscode_settings_path.exists():
            with open(vscode_settings_path, 'r') as f:
                current_settings = json.load(f)
        
        # 更新 Augment 提示词
        current_settings["augment.chat.userGuidelines"] = simple_prompts
        
        # 保存设置
        with open(vscode_settings_path, 'w') as f:
            json.dump(current_settings, f, indent=2, ensure_ascii=False)
        
        print("✅ Augment 简化提示词已应用")
        print(f"📝 提示词长度: {len(simple_prompts)} 字符")
        return True
        
    except Exception as e:
        print(f"❌ 应用提示词失败: {e}")
        return False


def clear_augment_prompts():
    """清除 Augment 提示词"""
    vscode_settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
    
    try:
        if vscode_settings_path.exists():
            with open(vscode_settings_path, 'r') as f:
                current_settings = json.load(f)
            
            # 清除提示词
            current_settings["augment.chat.userGuidelines"] = ""
            
            with open(vscode_settings_path, 'w') as f:
                json.dump(current_settings, f, indent=2)
            
            print("✅ Augment 提示词已清除")
            return True
    except Exception as e:
        print(f"❌ 清除提示词失败: {e}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="快速设置 AI 助手提示词")
    parser.add_argument("--apply", action="store_true", help="应用简化提示词")
    parser.add_argument("--clear", action="store_true", help="清除提示词")
    
    args = parser.parse_args()
    
    if args.apply:
        print("🎯 应用 Augment 简化提示词")
        print("=" * 30)
        
        if apply_simple_augment_prompts():
            print("\n🎉 提示词设置成功!")
            print("\n📋 下一步:")
            print("1. 重启 VS Code")
            print("2. 开始使用 Augment，它现在有了工作指导原则")
            return 0
        else:
            return 1
    
    elif args.clear:
        print("🧹 清除 Augment 提示词")
        print("=" * 20)
        
        if clear_augment_prompts():
            print("\n✅ 提示词已清除")
            print("Augment 将使用默认行为")
            return 0
        else:
            return 1
    
    else:
        print("🎯 Augment 提示词快速设置")
        print("=" * 30)
        print()
        print("选项:")
        print("  --apply  应用简化的工作指导原则")
        print("  --clear  清除所有提示词")
        print()
        print("示例:")
        print("  python ai-tools-manager/tools/quick-setup-prompts.py --apply")
        print("  python ai-tools-manager/tools/quick-setup-prompts.py --clear")
        return 0


if __name__ == "__main__":
    sys.exit(main())
