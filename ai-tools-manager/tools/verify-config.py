#!/usr/bin/env python3
"""配置验证工具"""

import json
import subprocess
import sys
from pathlib import Path


def check_current_config():
    """检查当前配置状态"""
    print("🔍 当前 AI 工具配置状态")
    print("=" * 40)
    
    # 检查 VS Code 设置
    settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
    
    if not settings_path.exists():
        print("❌ VS Code 设置文件不存在")
        return False
    
    try:
        with open(settings_path, 'r') as f:
            settings = json.load(f)
        
        # 检查 Copilot 状态
        copilot_enabled = settings.get("github.copilot.enable", {}).get("*", False)
        copilot_chat_enabled = settings.get("github.copilot.chat.enable", False)
        mcp_discovery = settings.get("chat.mcp.discovery.enabled", False)
        
        print(f"GitHub Copilot 代码补全: {'✅ 启用' if copilot_enabled else '❌ 禁用'}")
        print(f"GitHub Copilot Chat: {'✅ 启用' if copilot_chat_enabled else '❌ 禁用'}")
        print(f"MCP 工具发现: {'✅ 启用' if mcp_discovery else '❌ 禁用'}")
        
        # 判断当前模式
        if not copilot_enabled and not copilot_chat_enabled and not mcp_discovery:
            current_mode = "augment-only"
        elif copilot_enabled and copilot_chat_enabled and mcp_discovery:
            current_mode = "copilot-only"
        elif not copilot_enabled and copilot_chat_enabled and mcp_discovery:
            current_mode = "hybrid"
        else:
            current_mode = "custom"
        
        print(f"\n🎯 当前模式: {current_mode}")
        
    except Exception as e:
        print(f"❌ 读取设置文件失败: {e}")
        return False
    
    # 检查 MCP 配置
    mcp_path = Path(".vscode/mcp.json")
    if mcp_path.exists():
        print(f"✅ MCP 配置文件存在: {mcp_path}")
        try:
            with open(mcp_path, 'r') as f:
                mcp_config = json.load(f)
            
            servers = mcp_config.get("servers", {})
            print(f"📊 配置的 MCP 服务器数量: {len(servers)}")
            for server_name in servers.keys():
                print(f"  - {server_name}")
                
        except Exception as e:
            print(f"⚠️  MCP 配置文件格式错误: {e}")
    else:
        print("ℹ️  MCP 配置文件不存在")
    
    # 检查 MCP 服务器
    try:
        result = subprocess.run(
            ["which", "augment-agent-mcp"],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            print("✅ Augment Agent MCP 服务器可用")
        else:
            print("❌ Augment Agent MCP 服务器不可用")
    except Exception:
        print("❌ 无法检查 MCP 服务器状态")
    
    return True


def main():
    """主函数"""
    if check_current_config():
        print("\n💡 提示:")
        print("如需切换配置，请运行:")
        print("  python ai-tools-manager/tools/switch-config.py")
        return 0
    else:
        return 1


if __name__ == "__main__":
    sys.exit(main())
