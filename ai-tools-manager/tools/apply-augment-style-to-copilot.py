#!/usr/bin/env python3
"""将 Augment Agent 风格应用到 Copilot"""

import sys
from pathlib import Path


def create_copilot_instructions():
    """创建基于 Augment Agent 风格的 Copilot 指令"""
    
    # 读取 Augment 风格指导
    augment_style_path = Path(__file__).parent.parent / "prompts/copilot/augment-style-guidelines.md"
    
    if not augment_style_path.exists():
        print("❌ Augment 风格指导文件不存在")
        return False
    
    try:
        with open(augment_style_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 创建项目级的 Copilot 指令文件
        copilot_instructions_path = Path(".copilot-instructions.md")
        
        with open(copilot_instructions_path, 'w', encoding='utf-8') as f:
            f.write("# GitHub Copilot 项目指令\n\n")
            f.write("## 🎯 基于 Augment Agent 的工作风格\n\n")
            f.write("请按照以下指导原则工作，这些原则基于 Augment Agent 的高质量标准：\n\n")
            f.write(content)
        
        print(f"✅ Copilot 指令已创建: {copilot_instructions_path}")
        print(f"📝 指令长度: {len(content)} 字符")
        
        # 创建工作区级别的设置
        vscode_dir = Path(".vscode")
        vscode_dir.mkdir(exist_ok=True)
        
        settings_path = vscode_dir / "settings.json"
        
        # 如果设置文件不存在，创建一个基本的
        if not settings_path.exists():
            import json
            workspace_settings = {
                "github.copilot.chat.welcomeMessage": "never",
                "github.copilot.chat.localeOverride": "zh-CN",
                "files.autoSave": "afterDelay"
            }
            
            with open(settings_path, 'w') as f:
                json.dump(workspace_settings, f, indent=2)
            
            print(f"✅ 工作区设置已创建: {settings_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建 Copilot 指令失败: {e}")
        return False


def show_usage_instructions():
    """显示使用说明"""
    print("\n📋 使用说明:")
    print("=" * 30)
    print("1. 重启 VS Code")
    print("2. 打开 Copilot Chat")
    print("3. 选择 'Agent' 模式")
    print("4. Copilot 现在会按照 Augment Agent 的风格工作")
    print()
    print("🎯 期待的改进:")
    print("- 更详细的中文解释")
    print("- 更好的代码质量建议")
    print("- 更符合项目规范的建议")
    print("- 更主动的改进提案")


def verify_application():
    """验证应用是否成功"""
    copilot_instructions_path = Path(".copilot-instructions.md")
    
    if copilot_instructions_path.exists():
        print("\n🔍 验证结果:")
        print("✅ Copilot 指令文件存在")
        
        try:
            with open(copilot_instructions_path, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"✅ 指令内容长度: {len(content)} 字符")
            
            # 显示预览
            preview = content[:300] + "..." if len(content) > 300 else content
            print(f"📝 内容预览:\n{preview}")
            
            return True
        except Exception as e:
            print(f"❌ 读取指令文件失败: {e}")
            return False
    else:
        print("❌ Copilot 指令文件不存在")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="将 Augment Agent 风格应用到 Copilot")
    parser.add_argument("--apply", action="store_true", help="应用 Augment 风格到 Copilot")
    parser.add_argument("--verify", action="store_true", help="验证应用状态")
    
    args = parser.parse_args()
    
    if args.apply:
        print("🎯 将 Augment Agent 风格应用到 Copilot")
        print("=" * 45)
        
        if create_copilot_instructions():
            print("\n🎉 Augment 风格已成功应用到 Copilot!")
            show_usage_instructions()
            return 0
        else:
            return 1
    
    elif args.verify:
        print("🔍 验证 Copilot 风格应用状态")
        print("=" * 30)
        
        if verify_application():
            print("\n✅ Augment 风格已正确应用到 Copilot")
            return 0
        else:
            print("\n❌ Augment 风格未应用或应用失败")
            return 1
    
    else:
        print("🎯 Augment Agent 风格 → Copilot")
        print("=" * 35)
        print()
        print("这个工具可以将 Augment Agent 的工作风格应用到 GitHub Copilot，")
        print("让 Copilot 按照类似的高质量标准工作。")
        print()
        print("选项:")
        print("  --apply   应用 Augment 风格到 Copilot")
        print("  --verify  验证应用状态")
        print()
        print("示例:")
        print("  python ai-tools-manager/tools/apply-augment-style-to-copilot.py --apply")
        print("  python ai-tools-manager/tools/apply-augment-style-to-copilot.py --verify")
        return 0


if __name__ == "__main__":
    sys.exit(main())
