#!/usr/bin/env python3
"""设置全局 MCP 配置"""

import json
import sys
from pathlib import Path


def setup_global_mcp():
    """设置全局 MCP 配置"""
    
    print("🌍 设置全局 MCP 配置")
    print("=" * 25)
    
    vscode_settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
    
    try:
        # 读取当前设置
        current_settings = {}
        if vscode_settings_path.exists():
            with open(vscode_settings_path, 'r') as f:
                current_settings = json.load(f)
        
        # 添加全局 MCP 配置
        global_mcp_config = {
            "chat.mcp.discovery.enabled": True,
            "chat.mcp.servers": {
                "augment-agent": {
                    "command": "augment-agent-mcp",
                    "args": [],
                    "env": {
                        "AUGMENT_WORKSPACE": "${workspaceFolder}"
                    }
                }
            }
        }
        
        # 合并配置
        current_settings.update(global_mcp_config)
        
        # 保存设置
        with open(vscode_settings_path, 'w') as f:
            json.dump(current_settings, f, indent=2, ensure_ascii=False)
        
        print("✅ 全局 MCP 配置已应用")
        print(f"📝 配置位置: {vscode_settings_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置全局配置失败: {e}")
        return False


def remove_global_mcp():
    """移除全局 MCP 配置"""
    
    print("🧹 移除全局 MCP 配置")
    print("=" * 25)
    
    vscode_settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
    
    try:
        if vscode_settings_path.exists():
            with open(vscode_settings_path, 'r') as f:
                current_settings = json.load(f)
            
            # 移除 MCP 相关配置
            keys_to_remove = [
                "chat.mcp.servers",
                "chat.mcp.discovery.enabled"
            ]
            
            removed_count = 0
            for key in keys_to_remove:
                if key in current_settings:
                    del current_settings[key]
                    removed_count += 1
                    print(f"✅ 移除配置: {key}")
            
            if removed_count > 0:
                with open(vscode_settings_path, 'w') as f:
                    json.dump(current_settings, f, indent=2)
                print(f"✅ 已移除 {removed_count} 个配置项")
            else:
                print("ℹ️  没有找到需要移除的配置")
            
            return True
        else:
            print("ℹ️  设置文件不存在")
            return True
            
    except Exception as e:
        print(f"❌ 移除配置失败: {e}")
        return False


def check_global_config():
    """检查全局配置状态"""
    
    print("🔍 检查全局 MCP 配置")
    print("=" * 25)
    
    vscode_settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
    
    if not vscode_settings_path.exists():
        print("❌ VS Code 设置文件不存在")
        return False
    
    try:
        with open(vscode_settings_path, 'r') as f:
            settings = json.load(f)
        
        # 检查 MCP 配置
        mcp_discovery = settings.get("chat.mcp.discovery.enabled", False)
        mcp_servers = settings.get("chat.mcp.servers", {})
        
        print(f"MCP 发现功能: {'✅ 启用' if mcp_discovery else '❌ 禁用'}")
        
        if "augment-agent" in mcp_servers:
            print("✅ Augment Agent 服务器已配置")
            server_config = mcp_servers["augment-agent"]
            print(f"  命令: {server_config.get('command', '未设置')}")
            print(f"  工作区: {server_config.get('env', {}).get('AUGMENT_WORKSPACE', '未设置')}")
        else:
            print("❌ Augment Agent 服务器未配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="全局 MCP 配置管理")
    parser.add_argument("--setup", action="store_true", help="设置全局 MCP 配置")
    parser.add_argument("--remove", action="store_true", help="移除全局 MCP 配置")
    parser.add_argument("--check", action="store_true", help="检查配置状态")
    
    args = parser.parse_args()
    
    if args.setup:
        if setup_global_mcp():
            print("\n🎉 全局 MCP 配置设置成功!")
            print("\n📋 优势:")
            print("- 所有项目自动支持 MCP 工具")
            print("- 无需为每个项目单独配置")
            print("- 工作区路径自动适配")
            print("\n📋 下一步:")
            print("1. 重启 VS Code")
            print("2. 打开任意项目")
            print("3. Copilot Chat 中选择 'Agent' 模式")
            print("4. 开始使用 MCP 工具")
            return 0
        else:
            return 1
    
    elif args.remove:
        if remove_global_mcp():
            print("\n✅ 全局 MCP 配置已移除")
            print("现在需要为每个项目单独配置 MCP")
            return 0
        else:
            return 1
    
    elif args.check:
        check_global_config()
        return 0
    
    else:
        print("🌍 全局 MCP 配置管理器")
        print("=" * 30)
        print()
        print("这个工具可以设置全局 MCP 配置，让所有项目都支持 MCP 工具。")
        print()
        print("选项:")
        print("  --setup   设置全局 MCP 配置 (推荐)")
        print("  --remove  移除全局 MCP 配置")
        print("  --check   检查当前配置状态")
        print()
        print("示例:")
        print("  python ai-tools-manager/tools/setup-global-mcp.py --setup")
        print("  python ai-tools-manager/tools/setup-global-mcp.py --check")
        return 0


if __name__ == "__main__":
    sys.exit(main())
