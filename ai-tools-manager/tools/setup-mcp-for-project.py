#!/usr/bin/env python3
"""为新项目设置 MCP 工具"""

import json
import shutil
import sys
from pathlib import Path


def setup_mcp_for_current_project():
    """为当前项目设置 MCP 工具"""
    
    print("🔧 为当前项目设置 MCP 工具")
    print("=" * 30)
    
    current_dir = Path.cwd()
    print(f"📁 当前项目: {current_dir}")
    
    # 创建 .vscode 目录
    vscode_dir = current_dir / ".vscode"
    vscode_dir.mkdir(exist_ok=True)
    print(f"✅ 创建目录: {vscode_dir}")
    
    # 创建 MCP 配置文件
    mcp_config = {
        "inputs": [
            {
                "type": "promptString"
            }
        ],
        "servers": {
            "augment-agent": {
                "command": "augment-agent-mcp",
                "args": [],
                "env": {
                    "AUGMENT_WORKSPACE": str(current_dir)
                }
            }
        }
    }
    
    mcp_json_path = vscode_dir / "mcp.json"
    with open(mcp_json_path, 'w') as f:
        json.dump(mcp_config, f, indent=2)
    
    print(f"✅ 创建 MCP 配置: {mcp_json_path}")
    
    # 创建项目级 VS Code 设置
    settings_config = {
        "chat.mcp.discovery.enabled": True,
        "files.autoSave": "afterDelay"
    }
    
    settings_json_path = vscode_dir / "settings.json"
    if settings_json_path.exists():
        # 如果设置文件已存在，合并配置
        try:
            with open(settings_json_path, 'r') as f:
                existing_settings = json.load(f)
            existing_settings.update(settings_config)
            settings_config = existing_settings
        except:
            pass
    
    with open(settings_json_path, 'w') as f:
        json.dump(settings_config, f, indent=2)
    
    print(f"✅ 创建项目设置: {settings_json_path}")
    
    return True


def check_mcp_server_availability():
    """检查 MCP 服务器是否可用"""
    import subprocess
    
    try:
        result = subprocess.run(
            ["which", "augment-agent-mcp"],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            print("✅ MCP 服务器命令可用")
            return True
        else:
            print("❌ MCP 服务器命令不可用")
            print("💡 请确保在原项目中运行过安装命令")
            return False
    except Exception as e:
        print(f"❌ 检查 MCP 服务器失败: {e}")
        return False


def show_next_steps():
    """显示后续步骤"""
    print("\n📋 下一步操作:")
    print("=" * 20)
    print("1. 重启 VS Code")
    print("2. 在 VS Code 中打开当前项目")
    print("3. 查看 .vscode/mcp.json 文件")
    print("4. 点击文件顶部的 'Start' 按钮")
    print("5. 等待状态显示为 'Running'")
    print("6. 打开 Copilot Chat，选择 'Agent' 模式")
    print("7. 点击工具图标 🔧 查看可用工具")
    print("8. 开始使用: '查看当前项目文件'")


def verify_setup():
    """验证设置"""
    current_dir = Path.cwd()
    
    print("\n🔍 验证设置:")
    print("=" * 15)
    
    # 检查文件是否存在
    mcp_json = current_dir / ".vscode" / "mcp.json"
    settings_json = current_dir / ".vscode" / "settings.json"
    
    if mcp_json.exists():
        print("✅ MCP 配置文件存在")
        try:
            with open(mcp_json, 'r') as f:
                config = json.load(f)
            if "augment-agent" in config.get("servers", {}):
                print("✅ Augment Agent 服务器已配置")
            else:
                print("⚠️  Augment Agent 服务器配置缺失")
        except:
            print("❌ MCP 配置文件格式错误")
    else:
        print("❌ MCP 配置文件不存在")
    
    if settings_json.exists():
        print("✅ 项目设置文件存在")
    else:
        print("❌ 项目设置文件不存在")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="为新项目设置 MCP 工具")
    parser.add_argument("--setup", action="store_true", help="设置当前项目")
    parser.add_argument("--verify", action="store_true", help="验证设置")
    parser.add_argument("--check", action="store_true", help="检查 MCP 服务器")
    
    args = parser.parse_args()
    
    if args.setup:
        # 检查 MCP 服务器
        if not check_mcp_server_availability():
            print("\n⚠️  MCP 服务器不可用，但仍会创建配置文件")
            print("请确保已在原项目中安装 MCP 服务器")
        
        # 设置项目
        if setup_mcp_for_current_project():
            print("\n🎉 MCP 工具设置完成!")
            show_next_steps()
            return 0
        else:
            return 1
    
    elif args.verify:
        verify_setup()
        return 0
    
    elif args.check:
        check_mcp_server_availability()
        return 0
    
    else:
        print("🔧 MCP 工具项目设置器")
        print("=" * 25)
        print()
        print("这个工具可以为新项目快速设置 MCP 工具支持。")
        print()
        print("使用方法:")
        print("1. 在新项目目录中运行此脚本")
        print("2. 重启 VS Code")
        print("3. 启动 MCP 服务器")
        print("4. 开始使用工具")
        print()
        print("选项:")
        print("  --setup   为当前项目设置 MCP 工具")
        print("  --verify  验证当前项目的设置")
        print("  --check   检查 MCP 服务器是否可用")
        print()
        print("示例:")
        print("  cd /path/to/new/project")
        print("  python /path/to/setup-mcp-for-project.py --setup")
        return 0


if __name__ == "__main__":
    sys.exit(main())
