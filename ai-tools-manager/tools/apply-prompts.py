#!/usr/bin/env python3
"""应用 AI 助手提示词工具"""

import argparse
import json
import sys
from pathlib import Path


class PromptManager:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.prompts_dir = self.base_dir / "prompts"
        self.vscode_settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
        
    def load_prompt_files(self, target):
        """加载指定目标的提示词文件"""
        target_dir = self.prompts_dir / target
        
        if not target_dir.exists():
            print(f"❌ 提示词目录不存在: {target_dir}")
            return None
        
        prompts = {}
        
        # 加载所有 .md 文件
        for prompt_file in target_dir.glob("*.md"):
            try:
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                prompts[prompt_file.stem] = content
                print(f"✅ 加载提示词: {prompt_file.name}")
            except Exception as e:
                print(f"⚠️  加载提示词失败 {prompt_file.name}: {e}")
        
        return prompts
    
    def combine_prompts(self, prompts):
        """合并多个提示词文件"""
        if not prompts:
            return ""
        
        combined = []
        
        # 按优先级排序
        priority_order = [
            "user-preferences",
            "coding-guidelines", 
            "project-context"
        ]
        
        # 先添加优先级高的
        for key in priority_order:
            if key in prompts:
                combined.append(f"# {key.replace('-', ' ').title()}")
                combined.append(prompts[key])
                combined.append("")  # 空行分隔
        
        # 添加其他提示词
        for key, content in prompts.items():
            if key not in priority_order:
                combined.append(f"# {key.replace('-', ' ').title()}")
                combined.append(content)
                combined.append("")
        
        return "\n".join(combined)
    
    def apply_augment_prompts(self, prompts_text):
        """应用 Augment 提示词"""
        try:
            # 读取当前设置
            current_settings = {}
            if self.vscode_settings_path.exists():
                with open(self.vscode_settings_path, 'r') as f:
                    current_settings = json.load(f)
            
            # 更新 Augment 提示词
            current_settings["augment.chat.userGuidelines"] = prompts_text
            
            # 保存设置
            with open(self.vscode_settings_path, 'w') as f:
                json.dump(current_settings, f, indent=2, ensure_ascii=False)
            
            print("✅ Augment 提示词已应用")
            print(f"📝 提示词长度: {len(prompts_text)} 字符")
            return True
            
        except Exception as e:
            print(f"❌ 应用 Augment 提示词失败: {e}")
            return False
    
    def apply_copilot_prompts(self, prompts_text):
        """应用 Copilot 提示词 (通过项目级配置)"""
        try:
            # 创建项目级的 Copilot 指令文件
            copilot_instructions_path = Path(".copilot-instructions.md")
            
            with open(copilot_instructions_path, 'w', encoding='utf-8') as f:
                f.write("# GitHub Copilot 项目指令\n\n")
                f.write(prompts_text)
            
            print(f"✅ Copilot 提示词已保存到: {copilot_instructions_path}")
            print(f"📝 提示词长度: {len(prompts_text)} 字符")
            print("💡 重启 VS Code 后生效")
            return True
            
        except Exception as e:
            print(f"❌ 应用 Copilot 提示词失败: {e}")
            return False
    
    def view_current_prompts(self):
        """查看当前应用的提示词"""
        print("🔍 当前应用的提示词")
        print("=" * 40)
        
        # 查看 Augment 提示词
        try:
            if self.vscode_settings_path.exists():
                with open(self.vscode_settings_path, 'r') as f:
                    settings = json.load(f)
                
                augment_prompts = settings.get("augment.chat.userGuidelines", "")
                if augment_prompts:
                    print(f"✅ Augment 提示词: {len(augment_prompts)} 字符")
                    print("预览:")
                    preview = augment_prompts[:200] + "..." if len(augment_prompts) > 200 else augment_prompts
                    print(f"  {preview}")
                else:
                    print("ℹ️  Augment 提示词: 未设置")
            else:
                print("❌ VS Code 设置文件不存在")
        except Exception as e:
            print(f"❌ 读取 Augment 提示词失败: {e}")
        
        print()
        
        # 查看 Copilot 提示词
        copilot_instructions_path = Path(".copilot-instructions.md")
        if copilot_instructions_path.exists():
            try:
                with open(copilot_instructions_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"✅ Copilot 提示词: {len(content)} 字符")
                print("预览:")
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"  {preview}")
            except Exception as e:
                print(f"❌ 读取 Copilot 提示词失败: {e}")
        else:
            print("ℹ️  Copilot 提示词: 未设置")
    
    def list_available_prompts(self):
        """列出可用的提示词"""
        print("📋 可用的提示词模板")
        print("=" * 30)
        
        for target_dir in self.prompts_dir.iterdir():
            if target_dir.is_dir():
                print(f"\n📁 {target_dir.name}:")
                
                prompt_files = list(target_dir.glob("*.md"))
                if prompt_files:
                    for prompt_file in sorted(prompt_files):
                        print(f"  - {prompt_file.stem}")
                else:
                    print("  (无提示词文件)")


def main():
    parser = argparse.ArgumentParser(description="AI 助手提示词管理工具")
    parser.add_argument("--target", choices=["augment", "copilot"], 
                       help="目标 AI 助手")
    parser.add_argument("--view", action="store_true", 
                       help="查看当前提示词")
    parser.add_argument("--list", action="store_true", 
                       help="列出可用提示词")
    
    args = parser.parse_args()
    
    manager = PromptManager()
    
    if args.view:
        manager.view_current_prompts()
        return 0
    
    if args.list:
        manager.list_available_prompts()
        return 0
    
    if args.target:
        print(f"🎯 应用 {args.target} 提示词")
        print("=" * 30)
        
        # 加载提示词
        prompts = manager.load_prompt_files(args.target)
        if not prompts:
            return 1
        
        # 合并提示词
        combined_prompts = manager.combine_prompts(prompts)
        
        # 应用提示词
        if args.target == "augment":
            success = manager.apply_augment_prompts(combined_prompts)
        elif args.target == "copilot":
            success = manager.apply_copilot_prompts(combined_prompts)
        
        if success:
            print("\n🎉 提示词应用成功!")
            print("\n📋 下一步:")
            if args.target == "augment":
                print("1. 重启 VS Code")
                print("2. 开始使用 Augment")
            else:
                print("1. 重启 VS Code") 
                print("2. 开始使用 Copilot Chat")
            return 0
        else:
            return 1
    
    # 默认显示帮助
    parser.print_help()
    return 0


if __name__ == "__main__":
    sys.exit(main())
