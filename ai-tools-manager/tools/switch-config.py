#!/usr/bin/env python3
"""AI 工具配置切换器"""

import argparse
import json
import shutil
import sys
from pathlib import Path


class ConfigSwitcher:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.configs_dir = self.base_dir / "configs"
        self.vscode_settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
        self.vscode_mcp_path = Path(".vscode/mcp.json")
        self.backup_dir = self.base_dir / "backups"
        
    def backup_current_config(self):
        """备份当前配置"""
        self.backup_dir.mkdir(exist_ok=True)
        
        # 备份 VS Code 设置
        if self.vscode_settings_path.exists():
            backup_settings = self.backup_dir / f"settings-{self.get_timestamp()}.json"
            shutil.copy2(self.vscode_settings_path, backup_settings)
            print(f"✅ 已备份 VS Code 设置到: {backup_settings}")
        
        # 备份 MCP 配置
        if self.vscode_mcp_path.exists():
            backup_mcp = self.backup_dir / f"mcp-{self.get_timestamp()}.json"
            shutil.copy2(self.vscode_mcp_path, backup_mcp)
            print(f"✅ 已备份 MCP 配置到: {backup_mcp}")
    
    def get_timestamp(self):
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d-%H%M%S")
    
    def list_available_configs(self):
        """列出可用配置"""
        configs = []
        for config_dir in self.configs_dir.iterdir():
            if config_dir.is_dir() and (config_dir / "vscode-settings.json").exists():
                configs.append(config_dir.name)
        return configs
    
    def switch_to_config(self, config_name):
        """切换到指定配置"""
        config_dir = self.configs_dir / config_name
        
        if not config_dir.exists():
            print(f"❌ 配置 '{config_name}' 不存在")
            return False
        
        print(f"🔄 切换到配置: {config_name}")
        
        # 备份当前配置
        self.backup_current_config()
        
        # 应用新的 VS Code 设置
        new_settings_path = config_dir / "vscode-settings.json"
        if new_settings_path.exists():
            # 读取当前设置
            current_settings = {}
            if self.vscode_settings_path.exists():
                with open(self.vscode_settings_path, 'r') as f:
                    current_settings = json.load(f)
            
            # 读取新设置
            with open(new_settings_path, 'r') as f:
                new_settings = json.load(f)
            
            # 合并设置 (新设置覆盖相同的键)
            current_settings.update(new_settings)
            
            # 写入合并后的设置
            with open(self.vscode_settings_path, 'w') as f:
                json.dump(current_settings, f, indent=2)
            
            print(f"✅ 已应用 VS Code 设置")
        
        # 应用 MCP 配置
        new_mcp_path = config_dir / "mcp.json"
        if new_mcp_path.exists():
            # 确保 .vscode 目录存在
            self.vscode_mcp_path.parent.mkdir(exist_ok=True)
            
            shutil.copy2(new_mcp_path, self.vscode_mcp_path)
            print(f"✅ 已应用 MCP 配置")
        
        print(f"🎉 成功切换到 '{config_name}' 配置!")
        print("\n📋 下一步:")
        print("1. 重启 VS Code")
        print("2. 查看配置说明:")
        
        readme_path = config_dir / "README.md"
        if readme_path.exists():
            print(f"   cat {readme_path}")
        
        return True
    
    def interactive_switch(self):
        """交互式配置切换"""
        configs = self.list_available_configs()
        
        if not configs:
            print("❌ 没有找到可用的配置")
            return False
        
        print("🔧 AI 工具配置切换器")
        print("=" * 40)
        print("可用配置:")
        
        for i, config in enumerate(configs, 1):
            config_dir = self.configs_dir / config
            readme_path = config_dir / "README.md"
            
            description = config
            if readme_path.exists():
                # 尝试从 README 中提取描述
                try:
                    with open(readme_path, 'r') as f:
                        lines = f.readlines()
                        for line in lines:
                            if line.startswith("# "):
                                description = line[2:].strip()
                                break
                except:
                    pass
            
            print(f"{i}. {config} - {description}")
        
        print("0. 退出")
        
        try:
            choice = int(input("\n请选择配置 (输入数字): "))
            
            if choice == 0:
                print("👋 退出")
                return True
            
            if 1 <= choice <= len(configs):
                selected_config = configs[choice - 1]
                return self.switch_to_config(selected_config)
            else:
                print("❌ 无效选择")
                return False
                
        except ValueError:
            print("❌ 请输入有效数字")
            return False


def main():
    parser = argparse.ArgumentParser(description="AI 工具配置切换器")
    parser.add_argument("--mode", help="配置模式 (augment-only, copilot-only, hybrid)")
    parser.add_argument("--list", action="store_true", help="列出可用配置")
    parser.add_argument("--interactive", action="store_true", help="交互式选择")
    
    args = parser.parse_args()
    
    switcher = ConfigSwitcher()
    
    if args.list:
        configs = switcher.list_available_configs()
        print("可用配置:")
        for config in configs:
            print(f"  - {config}")
        return 0
    
    if args.mode:
        if switcher.switch_to_config(args.mode):
            return 0
        else:
            return 1
    
    # 默认使用交互式模式
    if switcher.interactive_switch():
        return 0
    else:
        return 1


if __name__ == "__main__":
    sys.exit(main())
