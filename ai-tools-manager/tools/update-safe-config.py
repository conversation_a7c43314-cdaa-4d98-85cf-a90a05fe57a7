#!/usr/bin/env python3
"""更新安全的配置管理器"""

import json
import sys
from pathlib import Path


class UpdateSafeConfigManager:
    """更新安全的配置管理器，只修改必要的设置"""
    
    def __init__(self):
        self.vscode_settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
        
        # 定义我们管理的配置键
        self.managed_keys = {
            "github.copilot.enable",
            "github.copilot.chat.enable", 
            "chat.mcp.discovery.enabled",
            # 只管理核心的 AI 相关设置，其他设置保持不变
        }
    
    def get_current_settings(self):
        """获取当前设置"""
        if not self.vscode_settings_path.exists():
            return {}
        
        try:
            with open(self.vscode_settings_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 读取设置失败: {e}")
            return {}
    
    def apply_minimal_changes(self, mode):
        """只应用最小必要的更改"""
        current_settings = self.get_current_settings()
        
        # 定义各模式的最小配置
        mode_configs = {
            "hybrid": {
                "github.copilot.enable": {"*": False},
                "github.copilot.chat.enable": True,
                "chat.mcp.discovery.enabled": True
            },
            "copilot-only": {
                "github.copilot.enable": {"*": True},
                "github.copilot.chat.enable": True,
                "chat.mcp.discovery.enabled": True
            },
            "augment-only": {
                "github.copilot.enable": {"*": False},
                "github.copilot.chat.enable": False,
                "chat.mcp.discovery.enabled": False
            }
        }
        
        if mode not in mode_configs:
            print(f"❌ 未知模式: {mode}")
            return False
        
        # 只更新我们管理的键
        target_config = mode_configs[mode]
        
        print(f"🔄 应用 {mode} 模式的最小配置更改...")
        
        for key, value in target_config.items():
            old_value = current_settings.get(key)
            current_settings[key] = value
            print(f"  {key}: {old_value} → {value}")
        
        # 保存更新后的设置
        try:
            with open(self.vscode_settings_path, 'w') as f:
                json.dump(current_settings, f, indent=2)
            print("✅ 配置更新成功")
            return True
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            return False
    
    def check_for_new_copilot_settings(self):
        """检查是否有新的 Copilot 设置"""
        current_settings = self.get_current_settings()
        
        # 查找所有 github.copilot 相关的设置
        copilot_keys = [key for key in current_settings.keys() if key.startswith("github.copilot")]
        
        print("🔍 当前的 Copilot 相关设置:")
        for key in sorted(copilot_keys):
            managed = "✅ 管理中" if key in self.managed_keys else "ℹ️  未管理"
            print(f"  {key}: {managed}")
        
        # 检查是否有新的设置
        unmanaged_keys = [key for key in copilot_keys if key not in self.managed_keys]
        
        if unmanaged_keys:
            print("\n⚠️  发现未管理的 Copilot 设置:")
            for key in unmanaged_keys:
                print(f"  - {key}")
            print("\n💡 这些设置不会被我们的工具修改，将保持 Copilot 的默认行为")
        else:
            print("\n✅ 所有重要的 Copilot 设置都在管理中")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="更新安全的配置管理器")
    parser.add_argument("--mode", choices=["hybrid", "copilot-only", "augment-only"], 
                       help="配置模式")
    parser.add_argument("--check", action="store_true", 
                       help="检查 Copilot 设置")
    
    args = parser.parse_args()
    
    manager = UpdateSafeConfigManager()
    
    if args.check:
        manager.check_for_new_copilot_settings()
        return 0
    
    if args.mode:
        if manager.apply_minimal_changes(args.mode):
            print(f"\n🎉 成功切换到 {args.mode} 模式")
            print("\n📋 重要提醒:")
            print("- 这种方法只修改核心 AI 设置")
            print("- Copilot 的其他设置保持不变")
            print("- 更新后新功能会自动可用")
            return 0
        else:
            return 1
    
    # 默认显示帮助
    parser.print_help()
    return 0


if __name__ == "__main__":
    sys.exit(main())
