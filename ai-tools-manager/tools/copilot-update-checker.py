#!/usr/bin/env python3
"""Copilot 更新兼容性检查工具"""

import json
import subprocess
import sys
from pathlib import Path


def check_copilot_version():
    """检查 Copilot 扩展版本"""
    print("🔍 检查 GitHub Copilot 扩展版本...")
    
    try:
        # 尝试通过 VS Code CLI 获取扩展信息
        result = subprocess.run(
            ["code", "--list-extensions", "--show-versions"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            copilot_extensions = [line for line in lines if 'github.copilot' in line.lower()]
            
            if copilot_extensions:
                print("✅ 找到 Copilot 扩展:")
                for ext in copilot_extensions:
                    print(f"  - {ext}")
                return True
            else:
                print("⚠️  未找到 Copilot 扩展")
                return False
        else:
            print("⚠️  无法通过 VS Code CLI 获取扩展信息")
            return False
            
    except Exception as e:
        print(f"⚠️  检查扩展版本时出错: {e}")
        return False


def check_mcp_compatibility():
    """检查 MCP 兼容性"""
    print("\n🔍 检查 MCP 兼容性...")
    
    # 检查 .vscode/mcp.json 格式
    mcp_path = Path(".vscode/mcp.json")
    if mcp_path.exists():
        try:
            with open(mcp_path, 'r') as f:
                mcp_config = json.load(f)
            
            # 检查是否符合当前 MCP 格式
            required_keys = ["servers"]
            optional_keys = ["inputs"]
            
            missing_keys = [key for key in required_keys if key not in mcp_config]
            
            if missing_keys:
                print(f"⚠️  MCP 配置缺少必需字段: {missing_keys}")
                return False
            else:
                print("✅ MCP 配置格式正确")
                
                # 检查服务器配置
                servers = mcp_config.get("servers", {})
                print(f"📊 配置的服务器数量: {len(servers)}")
                
                for server_name, server_config in servers.items():
                    if "command" in server_config:
                        print(f"  ✅ {server_name}: 配置完整")
                    else:
                        print(f"  ⚠️  {server_name}: 缺少 command 字段")
                
                return True
                
        except json.JSONDecodeError:
            print("❌ MCP 配置文件格式错误")
            return False
        except Exception as e:
            print(f"❌ 读取 MCP 配置失败: {e}")
            return False
    else:
        print("ℹ️  未找到 MCP 配置文件")
        return True  # 没有配置文件也是正常的


def check_settings_compatibility():
    """检查设置兼容性"""
    print("\n🔍 检查 VS Code 设置兼容性...")
    
    settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
    
    if not settings_path.exists():
        print("⚠️  VS Code 设置文件不存在")
        return False
    
    try:
        with open(settings_path, 'r') as f:
            settings = json.load(f)
        
        # 检查关键的 Copilot 设置
        copilot_settings = {
            "github.copilot.enable": settings.get("github.copilot.enable"),
            "github.copilot.chat.enable": settings.get("github.copilot.chat.enable"),
            "chat.mcp.discovery.enabled": settings.get("chat.mcp.discovery.enabled")
        }
        
        print("📋 当前 Copilot 设置:")
        for key, value in copilot_settings.items():
            if value is not None:
                print(f"  ✅ {key}: {value}")
            else:
                print(f"  ℹ️  {key}: 未设置 (使用默认值)")
        
        # 检查是否有过时的设置
        deprecated_keys = [
            "github.copilot.chat.mcp.servers"  # 旧的 MCP 配置格式
        ]
        
        found_deprecated = []
        for key in deprecated_keys:
            if key in settings:
                found_deprecated.append(key)
        
        if found_deprecated:
            print("\n⚠️  发现可能过时的设置:")
            for key in found_deprecated:
                print(f"  - {key}")
            print("💡 建议清理这些设置以避免冲突")
        else:
            print("\n✅ 未发现过时的设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查设置失败: {e}")
        return False


def suggest_update_actions():
    """建议更新操作"""
    print("\n💡 更新兼容性建议:")
    print("=" * 40)
    
    print("🔄 定期检查:")
    print("  1. 每月运行一次兼容性检查")
    print("  2. Copilot 更新后验证配置")
    print("  3. 关注 GitHub 的 MCP 文档更新")
    
    print("\n🛠️ 维护操作:")
    print("  1. 使用最小配置管理器:")
    print("     python ai-tools-manager/tools/update-safe-config.py --mode hybrid")
    print("  2. 检查新的 Copilot 设置:")
    print("     python ai-tools-manager/tools/update-safe-config.py --check")
    print("  3. 验证当前配置:")
    print("     python ai-tools-manager/tools/verify-config.py")
    
    print("\n🔗 保持更新:")
    print("  - GitHub Copilot 文档: https://docs.github.com/en/copilot")
    print("  - MCP 协议文档: https://modelcontextprotocol.io/")


def main():
    """主函数"""
    print("🔍 Copilot 更新兼容性检查")
    print("=" * 40)
    
    checks = [
        ("Copilot 扩展版本", check_copilot_version),
        ("MCP 兼容性", check_mcp_compatibility),
        ("设置兼容性", check_settings_compatibility),
    ]
    
    results = []
    for name, check_func in checks:
        result = check_func()
        results.append((name, result))
    
    print("\n" + "=" * 40)
    print("兼容性检查结果:")
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        if result:
            print(f"✅ {name}")
            passed += 1
        else:
            print(f"⚠️  {name}")
    
    print(f"\n📊 结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 所有检查通过！您的配置与 Copilot 更新兼容。")
    else:
        print(f"\n⚠️  {total - passed} 项需要注意，但不会影响基本功能。")
    
    suggest_update_actions()
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    sys.exit(main())
