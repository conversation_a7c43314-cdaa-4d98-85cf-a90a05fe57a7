# Augment Agent MCP Server

This MCP (Model Context Protocol) server exposes the powerful tools used by Augment Agent, making them available to Copilot and other MCP-compatible clients.

## Features

The server provides access to the following tool categories:

### File Operations
- **view_file**: View file and directory contents
- **save_file**: Create new files
- **edit_file**: Edit existing files using string replacement
- **remove_files**: Delete files safely

### Code Intelligence
- **codebase_search**: Search through codebase using Augment's context engine

### Process Management
- **launch_process**: Start new processes
- **read_process**: Read process output
- **write_process**: Write to process stdin
- **kill_process**: Terminate processes
- **list_processes**: List running processes

### Web Tools
- **web_search**: Search the web for information
- **web_fetch**: Fetch webpage content
- **open_browser**: Open URLs in browser

### Development Tools
- **get_diagnostics**: Get IDE diagnostics and issues
- **read_terminal**: Read terminal output

### Memory
- **remember**: Store information for future reference

## Installation

```bash
# Install using pip
pip install -e .

# Or using uv
uv pip install -e .
```

## Usage

### With Claude Desktop

Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "augment-agent": {
      "command": "augment-agent-mcp",
      "args": []
    }
  }
}
```

### With Other MCP Clients

The server uses stdio transport and can be launched with:

```bash
augment-agent-mcp
```

## Development

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Format code
black .
isort .

# Type checking
mypy .
```

## Security

This server provides powerful system access tools. Use only in trusted environments and ensure proper access controls are in place.
