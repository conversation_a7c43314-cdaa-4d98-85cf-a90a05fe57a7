#!/usr/bin/env python3
"""Check if the Augment Agent MCP Server is properly installed and configured."""

import subprocess
import sys
import json
from pathlib import Path


def check_python_version():
    """Check if Python version is 3.10+."""
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python version: {version.major}.{version.minor}.{version.micro} (requires 3.10+)")
        return False


def check_package_installation():
    """Check if the package is installed."""
    try:
        import augment_agent_mcp
        print(f"✅ Package installed: {augment_agent_mcp.__version__}")
        return True
    except ImportError:
        print("❌ Package not installed")
        return False


def check_command_line_tool():
    """Check if the command line tool is available."""
    try:
        # First check if the command exists
        result = subprocess.run(
            ["which", "augment-agent-mcp"],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            print("✅ Command line tool available")
            return True
        else:
            print("❌ Command line tool not found in PATH")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Command line tool not found")
        return False


def check_tools_registration():
    """Check if tools are properly registered."""
    try:
        from augment_agent_mcp.server import mcp
        if hasattr(mcp, '_tool_manager') and hasattr(mcp._tool_manager, '_tools'):
            tool_count = len(mcp._tool_manager._tools)
            if tool_count == 16:
                print(f"✅ All 16 tools registered")
                return True
            else:
                print(f"⚠️  Only {tool_count}/16 tools registered")
                return False
        else:
            print("❌ Cannot access tool registry")
            return False
    except Exception as e:
        print(f"❌ Error checking tools: {e}")
        return False


def check_claude_config():
    """Check if Claude Desktop config exists and is valid."""
    config_paths = [
        Path.home() / "Library/Application Support/Claude/claude_desktop_config.json",  # macOS
        Path.home() / "AppData/Roaming/Claude/claude_desktop_config.json",  # Windows
    ]

    for config_path in config_paths:
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)

                if "mcpServers" in config and "augment-agent" in config["mcpServers"]:
                    print(f"✅ Claude Desktop config found and configured: {config_path}")
                    return True
                else:
                    print(f"⚠️  Claude Desktop config found but not configured: {config_path}")
                    return False
            except json.JSONDecodeError:
                print(f"❌ Claude Desktop config invalid JSON: {config_path}")
                return False

    print("ℹ️  Claude Desktop config not found (optional)")
    return None


def check_dependencies():
    """Check if required dependencies are available."""
    dependencies = ["httpx", "aiofiles", "psutil", "mcp"]
    all_good = True

    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ Dependency available: {dep}")
        except ImportError:
            print(f"❌ Missing dependency: {dep}")
            all_good = False

    return all_good


def main():
    """Run all checks."""
    print("Augment Agent MCP Server - Installation Check")
    print("=" * 50)

    checks = [
        ("Python Version", check_python_version),
        ("Package Installation", check_package_installation),
        ("Dependencies", check_dependencies),
        ("Command Line Tool", check_command_line_tool),
        ("Tools Registration", check_tools_registration),
        ("Claude Desktop Config", check_claude_config),
    ]

    results = []
    for name, check_func in checks:
        print(f"\n{name}:")
        result = check_func()
        results.append((name, result))

    print("\n" + "=" * 50)
    print("Summary:")

    passed = 0
    total = 0
    for name, result in results:
        if result is True:
            print(f"✅ {name}")
            passed += 1
            total += 1
        elif result is False:
            print(f"❌ {name}")
            total += 1
        else:
            print(f"ℹ️  {name} (optional)")

    print(f"\nResult: {passed}/{total} checks passed")

    if passed == total:
        print("\n🎉 Installation is complete and ready to use!")
        print("\nNext steps:")
        print("1. Configure Claude Desktop (if not already done)")
        print("2. Restart Claude Desktop")
        print("3. Look for the tools icon in Claude Desktop")
        return 0
    else:
        print("\n⚠️  Some issues found. Please review the failed checks above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
