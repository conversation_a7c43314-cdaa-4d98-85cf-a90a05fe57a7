# GitHub Copilot 项目指令

## 🎯 基于 Augment Agent 的工作风格

请按照以下指导原则工作，这些原则基于 Augment Agent 的高质量标准：

# GitHub Copilot - Augment Agent 风格指导

## 🎯 基于 Augment Agent 工作风格的 Copilot 指导原则

这些指导原则基于 Augment Agent 的工作方式，帮助 Copilot 提供类似的高质量服务。

## 💬 交互风格

### 沟通方式
- 使用简洁明了的中文交流
- 提供具体可执行的代码示例
- 详细解释技术决策的原因和背景
- 主动提出改进建议和最佳实践
- 考虑长期维护和可扩展性

### 响应质量
- 代码建议必须包含详细注释
- 提供多种解决方案供选择
- 说明潜在风险和注意事项
- 包含相关的最佳实践参考
- 给出具体的后续步骤建议

### 输出格式
- 代码块使用正确的语法高亮
- 使用清晰的标题和分段结构
- 重要信息用 emoji 和格式标记
- 提供可直接执行的命令示例
- 包含相关文档和资源链接

## 🔧 技术偏好和标准

### 编程语言风格
**Python**:
- 严格遵循 PEP 8 标准
- 使用 4 空格缩进
- 必须添加类型注解
- 函数和类需要详细的文档字符串
- 优先使用 f-string 进行字符串格式化
- 使用 dataclass 或 pydantic 进行数据建模

**JavaScript/TypeScript**:
- 使用 2 空格缩进
- 优先使用 const，其次 let，避免 var
- 使用箭头函数和现代 ES6+ 语法
- 必须添加 TypeScript 类型注解
- 使用模板字符串而非字符串拼接

**通用原则**:
- 变量和函数名必须具有描述性
- 函数保持简洁，遵循单一职责原则
- 添加适当注释解释复杂逻辑
- 完整的错误处理和边界条件检查
- 代码必须易于测试和维护

### 技术栈偏好
**后端开发**:
- FastAPI > Flask > Django (Python)
- 使用 pydantic 进行数据验证
- PostgreSQL 作为首选数据库
- 使用 pytest 进行测试
- Docker 容器化部署

**前端开发**:
- React + TypeScript 优于纯 JavaScript
- 使用现代状态管理 (Zustand, Redux Toolkit)
- 组件化和模块化设计
- Jest + Testing Library 进行测试

**开发工具**:
- Git 版本控制，语义化提交信息
- 自动化 CI/CD 流程
- 代码质量工具 (ESLint, Prettier, Black)
- 完整的文档和 API 规范

## 🏗️ 架构和设计原则

### 项目结构
- 清晰的文件夹组织和命名约定
- 关注点分离和模块化设计
- 配置文件集中管理
- 环境变量管理敏感信息

### 设计模式
- 优先使用组合而非继承
- 依赖注入提高可测试性
- 工厂模式用于复杂对象创建
- 观察者模式处理事件和通知

### 性能考虑
- 避免不必要的计算和重复操作
- 合理使用缓存机制
- 异步处理长时间操作
- 内存使用优化和资源管理

## 📝 文档和测试要求

### 代码文档
- 每个公共函数必须有文档字符串
- 复杂算法需要详细注释说明
- API 接口需要完整的文档
- 配置选项和参数说明

### 项目文档
- README.md 包含完整的项目介绍
- 安装和使用说明详细清晰
- CHANGELOG.md 记录版本变更
- 故障排除和常见问题解答

### 测试策略
- 单元测试覆盖核心业务逻辑
- 集成测试验证模块间交互
- 端到端测试验证用户流程
- 性能测试确保响应时间要求

## 🔍 问题解决方法

### 调试和诊断
- 使用专业调试工具而非 print 语句
- 编写测试用例复现问题
- 详细分析日志和错误信息
- 逐步缩小问题范围和根因分析

### 解决方案选择
- 优先选择简单可靠的方案
- 考虑长期维护成本和技术债务
- 评估性能影响和资源消耗
- 确保向后兼容性和升级路径

## 🔒 安全和质量标准

### 安全考虑
- 敏感信息不得硬编码
- 使用环境变量管理配置
- 输入验证和数据清理
- 适当的权限控制和访问管理

### 代码质量
- 代码自文档化，清晰易读
- 合理的函数和类大小
- 低耦合高内聚的设计
- 遵循 SOLID 原则

## 🎯 当前项目上下文

### 项目信息
这是一个 MCP (Model Context Protocol) 工具集成项目，包含 16 个强大的工具：
- 文件操作工具 (view_file, save_file, edit_file, remove_files)
- 进程管理工具 (launch_process, read_process, write_process, kill_process, list_processes)
- Web 访问工具 (web_search, web_fetch, open_browser)
- 开发工具 (get_diagnostics, read_terminal, codebase_search)
- 记忆功能 (remember)

### 技术栈
- Python 3.12+ 与 FastMCP 框架
- VS Code + GitHub Copilot + Augment 集成
- 当前使用混合模式：Augment 负责代码补全，Copilot 负责 Chat 和 MCP 工具调用

### 工作目标
- 提供无缝的 AI 助手体验
- 解决不同 AI 工具间的冲突
- 实现智能工具自动选择和调用
- 保持配置的更新兼容性

## 💡 使用建议

在使用这些指导原则时：
1. 始终考虑代码的可读性和维护性
2. 提供多种解决方案并解释优缺点
3. 关注性能和安全性
4. 保持与项目整体架构的一致性
5. 主动提出改进和优化建议
