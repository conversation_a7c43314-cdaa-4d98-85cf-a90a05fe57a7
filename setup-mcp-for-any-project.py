#!/usr/bin/env python3
"""为任意项目设置 MCP 工具的通用脚本"""

import json
import sys
from pathlib import Path


def setup_mcp_for_project(project_path=None):
    """为指定项目设置 MCP 工具"""
    
    if project_path:
        project_dir = Path(project_path)
    else:
        project_dir = Path.cwd()
    
    print(f"🔧 为项目设置 MCP 工具: {project_dir}")
    print("=" * 50)
    
    # 创建 .vscode 目录
    vscode_dir = project_dir / ".vscode"
    vscode_dir.mkdir(exist_ok=True)
    print(f"✅ 创建目录: {vscode_dir}")
    
    # 创建 MCP 配置文件 (按照 GitHub 官方格式)
    mcp_config = {
        "inputs": [
            {
                "type": "promptString"
            }
        ],
        "servers": {
            "augment-agent": {
                "command": "augment-agent-mcp",
                "args": [],
                "env": {
                    "AUGMENT_WORKSPACE": str(project_dir)
                }
            }
        }
    }
    
    mcp_json_path = vscode_dir / "mcp.json"
    with open(mcp_json_path, 'w') as f:
        json.dump(mcp_config, f, indent=2)
    
    print(f"✅ 创建 MCP 配置: {mcp_json_path}")
    
    return True


def show_usage_instructions(project_dir):
    """显示使用说明"""
    print(f"\n📋 下一步操作:")
    print("=" * 20)
    print("1. 在 VS Code 中打开项目:")
    print(f"   code '{project_dir}'")
    print("2. 查看 .vscode/mcp.json 文件")
    print("3. 点击文件顶部的 'Start' 按钮")
    print("4. 等待状态显示为 'Running'")
    print("5. 打开 Copilot Chat，选择 'Agent' 模式")
    print("6. 点击工具图标 🔧 查看可用工具")
    print("7. 测试: '查看当前项目文件'")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="为任意项目设置 MCP 工具")
    parser.add_argument("project_path", nargs="?", help="项目路径 (默认为当前目录)")
    
    args = parser.parse_args()
    
    try:
        if setup_mcp_for_project(args.project_path):
            project_dir = Path(args.project_path) if args.project_path else Path.cwd()
            print("\n🎉 MCP 工具设置完成!")
            show_usage_instructions(project_dir)
            return 0
        else:
            return 1
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
