"""Main MCP server implementation for Augment Agent tools."""

import asyncio
import json
import logging
import os
import subprocess
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import httpx
from mcp.server.fastmcp import FastMCP

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastMCP server
mcp = FastMCP("augment-agent")

# Global state for process management
processes: Dict[int, subprocess.Popen] = {}
process_counter = 0


@mcp.tool()
async def view_file(
    path: str,
    view_range: Optional[List[int]] = None
) -> str:
    """View file or directory contents.

    Args:
        path: Path to file or directory relative to workspace root
        view_range: Optional [start_line, end_line] for files (1-based, inclusive)
    """
    try:
        file_path = Path(path)

        if file_path.is_dir():
            # List directory contents
            items = []
            try:
                for item in sorted(file_path.iterdir()):
                    if item.is_dir():
                        items.append(f"{item.name}/")
                    else:
                        items.append(item.name)
                return f"Directory contents of {path}:\n" + "\n".join(items)
            except PermissionError:
                return f"Permission denied accessing directory: {path}"

        elif file_path.is_file():
            # Read file contents
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                if view_range:
                    start_line, end_line = view_range
                    if end_line == -1:
                        end_line = len(lines)
                    # Convert to 0-based indexing
                    start_idx = max(0, start_line - 1)
                    end_idx = min(len(lines), end_line)
                    lines = lines[start_idx:end_idx]

                content = ''.join(lines)
                return f"File: {path}\n{'-' * 40}\n{content}"
            except UnicodeDecodeError:
                return f"Cannot read file {path}: Binary file or encoding issue"
            except PermissionError:
                return f"Permission denied reading file: {path}"

        else:
            return f"Path not found: {path}"

    except Exception as e:
        return f"Error accessing {path}: {str(e)}"


@mcp.tool()
async def save_file(path: str, content: str) -> str:
    """Save content to a new file.

    Args:
        path: Path where to save the file
        content: Content to write to the file
    """
    try:
        file_path = Path(path)

        # Create parent directories if they don't exist
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # Check if file already exists
        if file_path.exists():
            return f"Error: File {path} already exists. Use edit_file to modify existing files."

        # Write content to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return f"Successfully saved file: {path}"

    except Exception as e:
        return f"Error saving file {path}: {str(e)}"


@mcp.tool()
async def edit_file(
    path: str,
    old_str: str,
    new_str: str,
    old_str_start_line: int,
    old_str_end_line: int
) -> str:
    """Edit an existing file using string replacement.

    Args:
        path: Path to the file to edit
        old_str: String to replace (must match exactly)
        new_str: Replacement string
        old_str_start_line: Starting line number (1-based, inclusive)
        old_str_end_line: Ending line number (1-based, inclusive)
    """
    try:
        file_path = Path(path)

        if not file_path.exists():
            return f"Error: File {path} does not exist"

        # Read current content
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Convert to 0-based indexing
        start_idx = old_str_start_line - 1
        end_idx = old_str_end_line

        if start_idx < 0 or end_idx > len(lines):
            return f"Error: Line range {old_str_start_line}-{old_str_end_line} is out of bounds"

        # Extract the target section
        target_lines = lines[start_idx:end_idx]
        target_str = ''.join(target_lines)

        # Check if old_str matches
        if target_str != old_str:
            return f"Error: String to replace does not match exactly at lines {old_str_start_line}-{old_str_end_line}"

        # Perform replacement
        new_lines = new_str.split('\n')
        if new_str and not new_str.endswith('\n'):
            new_lines = [line + '\n' for line in new_lines[:-1]] + [new_lines[-1]]
        else:
            new_lines = [line + '\n' for line in new_lines]

        # Reconstruct file content
        result_lines = lines[:start_idx] + new_lines + lines[end_idx:]

        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(result_lines)

        return f"Successfully edited file: {path}"

    except Exception as e:
        return f"Error editing file {path}: {str(e)}"


@mcp.tool()
async def remove_files(file_paths: List[str]) -> str:
    """Remove files safely.

    Args:
        file_paths: List of file paths to remove
    """
    try:
        removed_files = []
        errors = []

        for path in file_paths:
            try:
                file_path = Path(path)
                if file_path.exists():
                    if file_path.is_file():
                        file_path.unlink()
                        removed_files.append(path)
                    else:
                        errors.append(f"{path}: Not a file")
                else:
                    errors.append(f"{path}: File not found")
            except Exception as e:
                errors.append(f"{path}: {str(e)}")

        result = []
        if removed_files:
            result.append(f"Successfully removed: {', '.join(removed_files)}")
        if errors:
            result.append(f"Errors: {'; '.join(errors)}")

        return '\n'.join(result) if result else "No files were removed"

    except Exception as e:
        return f"Error removing files: {str(e)}"


@mcp.tool()
async def launch_process(
    command: str,
    wait: bool = False,
    max_wait_seconds: float = 60.0,
    cwd: Optional[str] = None
) -> str:
    """Launch a new process.

    Args:
        command: Shell command to execute
        wait: Whether to wait for the process to complete
        max_wait_seconds: Maximum seconds to wait if wait=True
        cwd: Working directory for the command
    """
    global process_counter, processes

    try:
        process_counter += 1
        terminal_id = process_counter

        # Set working directory
        work_dir = Path(cwd) if cwd else Path.cwd()

        if wait:
            # Run and wait for completion
            try:
                result = subprocess.run(
                    command,
                    shell=True,
                    cwd=work_dir,
                    capture_output=True,
                    text=True,
                    timeout=max_wait_seconds
                )

                output = []
                if result.stdout:
                    output.append(f"STDOUT:\n{result.stdout}")
                if result.stderr:
                    output.append(f"STDERR:\n{result.stderr}")

                status = "completed" if result.returncode == 0 else f"failed (exit code: {result.returncode})"
                output.append(f"Process {status}")

                return '\n'.join(output)

            except subprocess.TimeoutExpired:
                return f"Process timed out after {max_wait_seconds} seconds"
            except Exception as e:
                return f"Error running process: {str(e)}"
        else:
            # Start background process
            process = subprocess.Popen(
                command,
                shell=True,
                cwd=work_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            processes[terminal_id] = process
            return f"Started background process with terminal_id: {terminal_id}"

    except Exception as e:
        return f"Error launching process: {str(e)}"


@mcp.tool()
async def read_process(
    terminal_id: int,
    wait: bool = False,
    max_wait_seconds: float = 60.0
) -> str:
    """Read output from a process.

    Args:
        terminal_id: Terminal ID to read from
        wait: Whether to wait for the process to complete
        max_wait_seconds: Maximum seconds to wait if wait=True
    """
    try:
        if terminal_id not in processes:
            return f"Process with terminal_id {terminal_id} not found"

        process = processes[terminal_id]

        if wait and process.poll() is None:
            # Wait for process to complete
            try:
                stdout, stderr = process.communicate(timeout=max_wait_seconds)
                output = []
                if stdout:
                    output.append(f"STDOUT:\n{stdout}")
                if stderr:
                    output.append(f"STDERR:\n{stderr}")

                # Clean up completed process
                del processes[terminal_id]

                return '\n'.join(output) if output else "Process completed with no output"

            except subprocess.TimeoutExpired:
                return f"Process did not complete within {max_wait_seconds} seconds"
        else:
            # Read available output
            if process.stdout and process.stdout.readable():
                try:
                    # Non-blocking read
                    import select
                    if select.select([process.stdout], [], [], 0)[0]:
                        output = process.stdout.read()
                        return output if output else "No output available"
                    else:
                        return "No output available"
                except:
                    return "Could not read process output"
            else:
                return "Process output not available"

    except Exception as e:
        return f"Error reading process: {str(e)}"


@mcp.tool()
async def write_process(terminal_id: int, input_text: str) -> str:
    """Write input to a process.

    Args:
        terminal_id: Terminal ID to write to
        input_text: Text to write to the process stdin
    """
    try:
        if terminal_id not in processes:
            return f"Process with terminal_id {terminal_id} not found"

        process = processes[terminal_id]

        if process.stdin and process.stdin.writable():
            process.stdin.write(input_text)
            process.stdin.flush()
            return f"Successfully wrote to process {terminal_id}"
        else:
            return f"Cannot write to process {terminal_id}: stdin not available"

    except Exception as e:
        return f"Error writing to process: {str(e)}"


@mcp.tool()
async def kill_process(terminal_id: int) -> str:
    """Kill a process by its terminal ID.

    Args:
        terminal_id: Terminal ID to kill
    """
    try:
        if terminal_id not in processes:
            return f"Process with terminal_id {terminal_id} not found"

        process = processes[terminal_id]
        process.terminate()

        # Wait a bit for graceful termination
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            # Force kill if it doesn't terminate gracefully
            process.kill()
            process.wait()

        del processes[terminal_id]
        return f"Successfully killed process {terminal_id}"

    except Exception as e:
        return f"Error killing process: {str(e)}"


@mcp.tool()
async def list_processes() -> str:
    """List all known processes created with launch_process."""
    try:
        if not processes:
            return "No active processes"

        result = []
        for terminal_id, process in processes.items():
            status = "running" if process.poll() is None else f"exited ({process.poll()})"
            result.append(f"Terminal ID {terminal_id}: {status}")

        return '\n'.join(result)

    except Exception as e:
        return f"Error listing processes: {str(e)}"


@mcp.tool()
async def web_search(query: str, num_results: int = 5) -> str:
    """Search the web for information.

    Args:
        query: The search query
        num_results: Number of results to return (1-10)
    """
    try:
        # This is a placeholder implementation
        # In a real implementation, you would integrate with a search API
        return f"Web search functionality not implemented. Query was: {query}, num_results: {num_results}"
    except Exception as e:
        return f"Error performing web search: {str(e)}"


@mcp.tool()
async def web_fetch(url: str) -> str:
    """Fetch content from a webpage.

    Args:
        url: The URL to fetch
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, timeout=30.0)
            response.raise_for_status()

            content_type = response.headers.get('content-type', '').lower()

            if 'text/html' in content_type:
                # For HTML, you might want to extract text content
                # This is a simplified version
                return f"Fetched HTML content from {url}:\n{response.text[:2000]}..."
            else:
                return f"Fetched content from {url}:\n{response.text[:2000]}..."

    except httpx.TimeoutException:
        return f"Timeout fetching {url}"
    except httpx.HTTPStatusError as e:
        return f"HTTP error fetching {url}: {e.response.status_code}"
    except Exception as e:
        return f"Error fetching {url}: {str(e)}"


@mcp.tool()
async def open_browser(url: str) -> str:
    """Open a URL in the default browser.

    Args:
        url: The URL to open
    """
    try:
        import webbrowser
        webbrowser.open(url)
        return f"Opened {url} in default browser"
    except Exception as e:
        return f"Error opening browser: {str(e)}"


@mcp.tool()
async def get_diagnostics(paths: Optional[List[str]] = None) -> str:
    """Get issues (errors, warnings, etc.) from the IDE.

    Args:
        paths: Optional list of file paths to get issues for
    """
    try:
        # This is a placeholder implementation
        # In a real implementation, you would integrate with IDE APIs
        if paths:
            return f"Diagnostics functionality not implemented. Requested paths: {', '.join(paths)}"
        else:
            return "Diagnostics functionality not implemented. No specific paths requested."
    except Exception as e:
        return f"Error getting diagnostics: {str(e)}"


@mcp.tool()
async def read_terminal(only_selected: bool = False) -> str:
    """Read output from the active terminal.

    Args:
        only_selected: Whether to read only selected text
    """
    try:
        # This is a placeholder implementation
        # In a real implementation, you would integrate with terminal APIs
        mode = "selected text" if only_selected else "all terminal output"
        return f"Terminal reading functionality not implemented. Mode: {mode}"
    except Exception as e:
        return f"Error reading terminal: {str(e)}"


@mcp.tool()
async def remember(memory: str) -> str:
    """Store information for future reference.

    Args:
        memory: The information to remember
    """
    try:
        # Simple file-based memory storage
        memory_file = Path.home() / ".augment_agent_memories.txt"

        with open(memory_file, 'a', encoding='utf-8') as f:
            from datetime import datetime
            timestamp = datetime.now().isoformat()
            f.write(f"[{timestamp}] {memory}\n")

        return f"Remembered: {memory}"
    except Exception as e:
        return f"Error storing memory: {str(e)}"


@mcp.tool()
async def codebase_search(information_request: str) -> str:
    """Search through the codebase using context retrieval.

    Args:
        information_request: Description of the information needed
    """
    try:
        # This is a placeholder implementation
        # In a real implementation, you would integrate with Augment's context engine
        return f"Codebase search functionality not implemented. Request was: {information_request}"
    except Exception as e:
        return f"Error searching codebase: {str(e)}"


def main() -> None:
    """Main entry point for the MCP server."""
    try:
        mcp.run(transport="stdio")
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
