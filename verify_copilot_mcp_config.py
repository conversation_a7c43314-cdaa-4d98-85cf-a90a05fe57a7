#!/usr/bin/env python3
"""Verify GitHub Copilot MCP configuration."""

import json
import os
import subprocess
import sys
from pathlib import Path


def check_mcp_server():
    """Check if MCP server is working."""
    print("🔧 Checking MCP Server...")
    try:
        result = subprocess.run(
            ["which", "augment-agent-mcp"],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            print("  ✅ MCP server command available")
            return True
        else:
            print("  ❌ MCP server command not found")
            return False
    except Exception as e:
        print(f"  ❌ Error checking MCP server: {e}")
        return False


def check_vscode_settings():
    """Check VS Code settings for Copilot MCP configuration."""
    print("\n🔧 Checking VS Code Settings...")
    
    settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
    
    if not settings_path.exists():
        print("  ❌ VS Code settings file not found")
        return False
    
    try:
        with open(settings_path, 'r') as f:
            settings = json.load(f)
        
        # Check if Copilot is enabled
        copilot_enabled = settings.get("github.copilot.enable", {}).get("*", False)
        if copilot_enabled:
            print("  ✅ GitHub Copilot enabled")
        else:
            print("  ⚠️  GitHub Copilot not enabled")
        
        # Check for MCP servers configuration
        mcp_servers = settings.get("github.copilot.chat.mcp.servers", {})
        if "augment-agent" in mcp_servers:
            print("  ✅ Augment Agent MCP server configured in VS Code")
            return True
        else:
            print("  ⚠️  Augment Agent MCP server not found in VS Code settings")
            return False
            
    except json.JSONDecodeError:
        print("  ❌ Invalid JSON in VS Code settings")
        return False
    except Exception as e:
        print(f"  ❌ Error reading VS Code settings: {e}")
        return False


def check_cline_settings():
    """Check Cline extension MCP settings."""
    print("\n🔧 Checking Cline Extension Settings...")
    
    cline_paths = [
        Path.home() / "Library/Application Support/Code/User/globalStorage/zhucan.debug-cline/settings/cline_mcp_settings.json",
        Path.home() / "Library/Application Support/Code/User/globalStorage/rooveterinaryinc.roo-cline/settings/mcp_settings.json"
    ]
    
    found_configs = 0
    
    for path in cline_paths:
        if path.exists():
            try:
                with open(path, 'r') as f:
                    config = json.load(f)
                
                if "augment-agent" in config.get("mcpServers", {}):
                    print(f"  ✅ Augment Agent configured in {path.parent.parent.name}")
                    found_configs += 1
                else:
                    print(f"  ⚠️  Augment Agent not configured in {path.parent.parent.name}")
                    
            except Exception as e:
                print(f"  ❌ Error reading {path.parent.parent.name}: {e}")
        else:
            print(f"  ℹ️  {path.parent.parent.name} not found (extension may not be installed)")
    
    return found_configs > 0


def test_mcp_connection():
    """Test MCP server connection."""
    print("\n🔧 Testing MCP Server Connection...")
    
    try:
        # Test initialize request
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {"name": "test-client", "version": "1.0.0"}
            }
        }
        
        process = subprocess.Popen(
            ["augment-agent-mcp"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        stdout, stderr = process.communicate(
            input=json.dumps(init_request) + "\n",
            timeout=10
        )
        
        if stdout:
            response = json.loads(stdout.strip())
            if "result" in response:
                print("  ✅ MCP server responds correctly")
                server_info = response["result"].get("serverInfo", {})
                print(f"    Server: {server_info.get('name', 'Unknown')}")
                print(f"    Version: {server_info.get('version', 'Unknown')}")
                return True
            else:
                print("  ❌ Invalid response from MCP server")
                return False
        else:
            print("  ❌ No response from MCP server")
            if stderr:
                print(f"    Error: {stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ❌ MCP server connection timeout")
        process.kill()
        return False
    except Exception as e:
        print(f"  ❌ Error testing MCP connection: {e}")
        return False


def main():
    """Run all configuration checks."""
    print("GitHub Copilot MCP Configuration Verification")
    print("=" * 50)
    
    checks = [
        ("MCP Server", check_mcp_server),
        ("VS Code Settings", check_vscode_settings),
        ("Cline Extensions", check_cline_settings),
        ("MCP Connection", test_mcp_connection),
    ]
    
    results = []
    for name, check_func in checks:
        result = check_func()
        results.append((name, result))
    
    print("\n" + "=" * 50)
    print("Configuration Summary:")
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        if result:
            print(f"✅ {name}")
            passed += 1
        else:
            print(f"❌ {name}")
    
    print(f"\nResult: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 All configurations are ready!")
        print("\nNext steps:")
        print("1. Restart VS Code")
        print("2. Open a project in VS Code")
        print("3. Try using Copilot Chat with commands like:")
        print("   - '@workspace list the files in this project'")
        print("   - '@workspace create a new Python file'")
        print("   - '@workspace run the tests'")
        return 0
    else:
        print(f"\n⚠️  {total - passed} configuration(s) need attention.")
        print("Please review the failed checks above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
