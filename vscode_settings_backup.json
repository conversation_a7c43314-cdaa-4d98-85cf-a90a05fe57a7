{"editor.fontVariations": false, "editor.fontSize": 11, "geminiCoder.presets": [{"name": "AI Studio with 2.0 Flash", "chatbot": "AI Studio", "promptPrefix": "", "promptSuffix": "", "model": "gemini-2.0-flash", "temperature": 0.5, "systemInstructions": "You're a helpful coding assistant. Whenever proposing a file use the file block syntax.\nFiles must be represented as code blocks with their `name` in the header.\nExample of a code block with a file name in the header:\n```typescript name=filename.ts\ncontents of file\n```"}, {"name": "AI Studio with 2.0 Flash Thinking Exp", "chatbot": "AI Studio", "promptPrefix": "", "promptSuffix": "", "model": "gemini-2.0-flash-thinking-exp-01-21", "temperature": 0.5, "systemInstructions": "You're a helpful coding assistant. Whenever proposing a file use the file block syntax.\nFiles must be represented as code blocks with their `name` in the header.\nExample of a code block with a file name in the header:\n```typescript name=filename.ts\ncontents of file\n```"}, {"name": "AI Studio with 2.5 Pro Exp", "chatbot": "AI Studio", "promptPrefix": "", "promptSuffix": "", "model": "gemini-2.5-pro-exp-03-25", "temperature": 0.5, "systemInstructions": "You're a helpful coding assistant. Whenever proposing a file use the file block syntax.\nFiles must be represented as code blocks with their `name` in the header.\nExample of a code block with a file name in the header:\n```typescript name=filename.ts\ncontents of file\n```"}, {"name": "Gemini", "chatbot": "Gemini", "promptPrefix": "", "promptSuffix": ""}, {"name": "Gemini with canvas", "chatbot": "Gemini", "promptPrefix": "", "promptSuffix": "", "options": ["canvas"]}, {"name": "ChatGPT", "chatbot": "ChatGPT", "promptPrefix": "", "promptSuffix": ""}, {"name": "<PERSON>", "chatbot": "<PERSON>", "promptPrefix": "", "promptSuffix": ""}, {"name": "GitHub Copilot", "chatbot": "GitHub Copilot", "promptPrefix": "", "promptSuffix": ""}, {"name": "Grok", "chatbot": "Grok", "promptPrefix": "", "promptSuffix": ""}, {"name": "DeepSeek", "chatbot": "DeepSeek", "promptPrefix": "", "promptSuffix": ""}, {"name": "<PERSON><PERSON><PERSON>", "chatbot": "<PERSON><PERSON><PERSON>", "promptPrefix": "", "promptSuffix": ""}, {"name": "Open WebUI", "chatbot": "Open WebUI", "promptPrefix": "", "promptSuffix": ""}], "geminiCoder.providers": [], "github.copilot.enable": {"*": false}, "files.autoSave": "after<PERSON>elay", "github.copilot.chat.languageContext.inline.typescript.enabled": true, "github.copilot.chat.languageContext.typescript.enabled": true, "github.copilot.chat.languageContext.fix.typescript.enabled": true, "roo-cline.allowedCommands": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"], "augment.chat.userGuidelines": ""}