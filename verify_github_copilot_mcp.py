#!/usr/bin/env python3
"""Verify GitHub Copilot MCP configuration according to official documentation."""

import json
import os
import subprocess
import sys
from pathlib import Path


def check_mcp_server():
    """Check if MCP server is working."""
    print("🔧 Checking MCP Server...")
    try:
        result = subprocess.run(
            ["which", "augment-agent-mcp"],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            print("  ✅ MCP server command available")
            return True
        else:
            print("  ❌ MCP server command not found")
            return False
    except Exception as e:
        print(f"  ❌ Error checking MCP server: {e}")
        return False


def check_vscode_mcp_json():
    """Check .vscode/mcp.json configuration."""
    print("\n🔧 Checking .vscode/mcp.json...")
    
    mcp_json_path = Path(".vscode/mcp.json")
    
    if not mcp_json_path.exists():
        print("  ❌ .vscode/mcp.json not found")
        return False
    
    try:
        with open(mcp_json_path, 'r') as f:
            config = json.load(f)
        
        # Check required structure
        if "servers" not in config:
            print("  ❌ 'servers' section missing")
            return False
        
        if "augment-agent" not in config["servers"]:
            print("  ❌ 'augment-agent' server not configured")
            return False
        
        server_config = config["servers"]["augment-agent"]
        if "command" not in server_config:
            print("  ❌ 'command' missing in server config")
            return False
        
        if server_config["command"] != "augment-agent-mcp":
            print("  ❌ Incorrect command in server config")
            return False
        
        print("  ✅ .vscode/mcp.json properly configured")
        return True
        
    except json.JSONDecodeError:
        print("  ❌ Invalid JSON in .vscode/mcp.json")
        return False
    except Exception as e:
        print(f"  ❌ Error reading .vscode/mcp.json: {e}")
        return False


def check_vscode_settings():
    """Check VS Code settings for MCP discovery."""
    print("\n🔧 Checking VS Code Settings...")
    
    settings_path = Path.home() / "Library/Application Support/Code/User/settings.json"
    
    if not settings_path.exists():
        print("  ❌ VS Code settings file not found")
        return False
    
    try:
        with open(settings_path, 'r') as f:
            settings = json.load(f)
        
        # Check if Copilot is enabled
        copilot_enabled = settings.get("github.copilot.enable", {}).get("*", False)
        if copilot_enabled:
            print("  ✅ GitHub Copilot enabled")
        else:
            print("  ❌ GitHub Copilot not enabled")
            return False
        
        # Check for MCP discovery
        mcp_discovery = settings.get("chat.mcp.discovery.enabled", False)
        if mcp_discovery:
            print("  ✅ MCP discovery enabled")
            return True
        else:
            print("  ⚠️  MCP discovery not enabled")
            return False
            
    except json.JSONDecodeError:
        print("  ❌ Invalid JSON in VS Code settings")
        return False
    except Exception as e:
        print(f"  ❌ Error reading VS Code settings: {e}")
        return False


def test_mcp_connection():
    """Test MCP server connection."""
    print("\n🔧 Testing MCP Server Connection...")
    
    try:
        # Test initialize request
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {"name": "test-client", "version": "1.0.0"}
            }
        }
        
        process = subprocess.Popen(
            ["augment-agent-mcp"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        stdout, stderr = process.communicate(
            input=json.dumps(init_request) + "\n",
            timeout=10
        )
        
        if stdout:
            response = json.loads(stdout.strip())
            if "result" in response:
                print("  ✅ MCP server responds correctly")
                server_info = response["result"].get("serverInfo", {})
                print(f"    Server: {server_info.get('name', 'Unknown')}")
                print(f"    Version: {server_info.get('version', 'Unknown')}")
                return True
            else:
                print("  ❌ Invalid response from MCP server")
                return False
        else:
            print("  ❌ No response from MCP server")
            if stderr:
                print(f"    Error: {stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ❌ MCP server connection timeout")
        process.kill()
        return False
    except Exception as e:
        print(f"  ❌ Error testing MCP connection: {e}")
        return False


def main():
    """Run all configuration checks."""
    print("GitHub Copilot MCP Configuration Verification")
    print("(According to Official GitHub Documentation)")
    print("=" * 60)
    
    checks = [
        ("MCP Server", check_mcp_server),
        (".vscode/mcp.json", check_vscode_mcp_json),
        ("VS Code Settings", check_vscode_settings),
        ("MCP Connection", test_mcp_connection),
    ]
    
    results = []
    for name, check_func in checks:
        result = check_func()
        results.append((name, result))
    
    print("\n" + "=" * 60)
    print("Configuration Summary:")
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        if result:
            print(f"✅ {name}")
            passed += 1
        else:
            print(f"❌ {name}")
    
    print(f"\nResult: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 GitHub Copilot MCP configuration is ready!")
        print("\nNext steps:")
        print("1. Restart VS Code completely")
        print("2. Open this project in VS Code")
        print("3. Look for the 'Start' button in .vscode/mcp.json")
        print("4. Click 'Start' to activate the MCP server")
        print("5. Open Copilot Chat and select 'Agent' mode")
        print("6. Click the tools icon to see available MCP tools")
        print("7. Try commands like:")
        print("   - 'List the files in this project'")
        print("   - 'Create a new Python file'")
        print("   - 'Run the tests'")
        return 0
    else:
        print(f"\n⚠️  {total - passed} configuration(s) need attention.")
        print("Please review the failed checks above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
