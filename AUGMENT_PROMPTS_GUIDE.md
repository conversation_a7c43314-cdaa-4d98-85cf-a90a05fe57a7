# 🎯 Augment 提示词配置指南

## ✅ 是的，Augment 有自己的工作提示词！

我已经为您创建了完整的 Augment 提示词管理系统。

## 📋 当前状态

### ✅ 已配置的提示词
- **长度**: 5,379 字符
- **内容**: 包含编码指导、技术偏好、项目上下文等
- **状态**: 已应用到 VS Code 设置中

### 📝 提示词内容包括
1. **代码风格偏好** - Python, TypeScript 等编码规范
2. **技术栈偏好** - FastAPI, React, PostgreSQL 等
3. **项目管理** - Git 提交、文档、测试要求
4. **交互风格** - 中文交流、详细解释、多方案选择
5. **当前项目上下文** - MCP 工具项目的具体信息

## 🔧 提示词管理工具

### 1. 查看当前提示词
```bash
python ai-tools-manager/tools/apply-prompts.py --view
```

### 2. 应用完整提示词
```bash
python ai-tools-manager/tools/apply-prompts.py --target augment
```

### 3. 快速应用简化版本
```bash
python ai-tools-manager/tools/quick-setup-prompts.py --apply
```

### 4. 清除提示词
```bash
python ai-tools-manager/tools/quick-setup-prompts.py --clear
```

## 📁 提示词文件结构

```
ai-tools-manager/prompts/augment/
├── coding-guidelines.md      # 编码指导原则
├── user-preferences.md       # 用户偏好设置  
├── project-context.md        # 项目上下文
└── simple-guidelines.md      # 简化版指导
```

## 🎯 提示词效果

### 编码时 Augment 会：
- ✅ 遵循您偏好的代码风格
- ✅ 使用您喜欢的技术栈
- ✅ 提供中文解释和建议
- ✅ 考虑当前项目的特定需求
- ✅ 给出多种解决方案

### 示例对比

**没有提示词时**:
```
Augment: 这里可以用一个函数
```

**有提示词后**:
```
Augment: 建议创建一个纯函数来处理这个逻辑，遵循单一职责原则。
可以使用 FastAPI 的依赖注入模式，添加类型注解提高代码可读性。
这样既符合项目的 Python 编码规范，也便于后续的单元测试。
```

## 🔄 提示词配置位置

提示词存储在 VS Code 设置中：
```json
{
  "augment.chat.userGuidelines": "您的提示词内容..."
}
```

位置：`~/Library/Application Support/Code/User/settings.json`

## 🚀 立即生效

### 方法 1: 重启 VS Code (推荐)
完全退出并重新启动 VS Code，提示词立即生效

### 方法 2: 重新加载窗口
在 VS Code 中按 `Cmd+Shift+P`，输入 "Reload Window"

## 💡 自定义提示词

### 编辑现有提示词
```bash
# 编辑用户偏好
code ai-tools-manager/prompts/augment/user-preferences.md

# 编辑编码指导
code ai-tools-manager/prompts/augment/coding-guidelines.md

# 重新应用
python ai-tools-manager/tools/apply-prompts.py --target augment
```

### 创建新的提示词文件
1. 在 `ai-tools-manager/prompts/augment/` 创建新的 `.md` 文件
2. 运行应用命令自动包含新文件

## 🎯 最佳实践

### 1. 保持提示词简洁
- 重点突出最重要的偏好
- 避免过于详细的规则

### 2. 定期更新
- 根据项目变化调整上下文
- 更新技术栈偏好

### 3. 测试效果
- 应用后测试 Augment 的响应
- 根据效果调整提示词

## 📊 当前配置总结

```
🔍 当前应用的提示词
========================================
✅ Augment 提示词: 5,379 字符
包含: 编码风格、技术偏好、项目上下文、交互偏好

📋 主要内容:
- Python/TypeScript 编码规范
- FastAPI/React 技术栈偏好  
- 中文交流，详细解释
- MCP 工具项目上下文
- 测试驱动开发要求
```

## 🎉 总结

**是的，Augment 现在有了完整的工作提示词！**

- ✅ **已配置**: 5,379 字符的详细指导原则
- ✅ **已生效**: 重启 VS Code 后立即可用
- ✅ **可管理**: 完整的工具链支持修改和更新
- ✅ **可定制**: 根据需要随时调整

现在 Augment 会按照您的偏好和项目需求提供更精准的建议！🚀
