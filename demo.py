#!/usr/bin/env python3
"""Demo script showing how to use the Augment Agent MCP Server tools."""

import asyncio
import json
import sys
from augment_agent_mcp.server import (
    view_file, save_file, edit_file, remove_files,
    launch_process, list_processes, 
    web_fetch, remember, codebase_search
)


async def demo_file_operations():
    """Demonstrate file operation tools."""
    print("=" * 50)
    print("File Operations Demo")
    print("=" * 50)
    
    # Create a demo file
    print("1. Creating a demo file...")
    result = await save_file("demo_file.txt", "Hello, MCP World!\nThis is a demo file.\nLine 3 content.")
    print(f"   {result}")
    
    # View the file
    print("\n2. Viewing the file...")
    result = await view_file("demo_file.txt")
    print(f"   {result}")
    
    # View specific lines
    print("\n3. Viewing lines 1-2...")
    result = await view_file("demo_file.txt", [1, 2])
    print(f"   {result}")
    
    # Edit the file
    print("\n4. Editing the file...")
    result = await edit_file(
        "demo_file.txt",
        "Line 3 content.",
        "Updated line 3 content with more text.",
        3, 3
    )
    print(f"   {result}")
    
    # View the edited file
    print("\n5. Viewing the edited file...")
    result = await view_file("demo_file.txt")
    print(f"   {result}")
    
    # Clean up
    print("\n6. Removing the demo file...")
    result = await remove_files(["demo_file.txt"])
    print(f"   {result}")


async def demo_process_management():
    """Demonstrate process management tools."""
    print("\n" + "=" * 50)
    print("Process Management Demo")
    print("=" * 50)
    
    # List current processes
    print("1. Listing current processes...")
    result = await list_processes()
    print(f"   {result}")
    
    # Launch a simple process
    print("\n2. Launching a simple command...")
    result = await launch_process("echo 'Hello from process'", wait=True, max_wait_seconds=5)
    print(f"   {result}")
    
    # Launch a background process
    print("\n3. Launching a background process...")
    result = await launch_process("sleep 2", wait=False)
    print(f"   {result}")
    
    # List processes again
    print("\n4. Listing processes after launch...")
    result = await list_processes()
    print(f"   {result}")


async def demo_web_tools():
    """Demonstrate web tools."""
    print("\n" + "=" * 50)
    print("Web Tools Demo")
    print("=" * 50)
    
    # Fetch a simple webpage
    print("1. Fetching a webpage...")
    try:
        result = await web_fetch("https://httpbin.org/json")
        print(f"   Success: {result[:200]}...")
    except Exception as e:
        print(f"   Error: {e}")


async def demo_memory_and_search():
    """Demonstrate memory and search tools."""
    print("\n" + "=" * 50)
    print("Memory and Search Demo")
    print("=" * 50)
    
    # Store a memory
    print("1. Storing a memory...")
    result = await remember("This demo was run successfully on the MCP server")
    print(f"   {result}")
    
    # Try codebase search (placeholder)
    print("\n2. Searching codebase...")
    result = await codebase_search("FastMCP server implementation")
    print(f"   {result}")


async def demo_directory_operations():
    """Demonstrate directory operations."""
    print("\n" + "=" * 50)
    print("Directory Operations Demo")
    print("=" * 50)
    
    # View current directory
    print("1. Viewing current directory...")
    result = await view_file(".")
    print(f"   {result}")
    
    # View a specific subdirectory if it exists
    print("\n2. Viewing augment_agent_mcp directory...")
    result = await view_file("augment_agent_mcp")
    print(f"   {result}")


async def main():
    """Run all demos."""
    print("Augment Agent MCP Server - Tool Demonstration")
    print("This demo shows how the various tools work.")
    print()
    
    try:
        # Run all demo sections
        await demo_file_operations()
        await demo_process_management()
        await demo_web_tools()
        await demo_memory_and_search()
        await demo_directory_operations()
        
        print("\n" + "=" * 50)
        print("Demo completed successfully!")
        print("All tools are working as expected.")
        print("=" * 50)
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
