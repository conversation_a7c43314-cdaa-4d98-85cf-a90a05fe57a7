{"mcpServers": {"augment-agent": {"command": "augment-agent-mcp", "args": [], "env": {"AUGMENT_WORKSPACE": "/Users/<USER>/Documents/augment-projects/mcp"}, "description": "Augment Agent tools for file operations, process management, web access, and development tasks", "tools": ["view_file", "save_file", "edit_file", "remove_files", "launch_process", "read_process", "write_process", "kill_process", "list_processes", "web_search", "web_fetch", "open_browser", "get_diagnostics", "read_terminal", "remember", "codebase_search"]}}}