# GitHub Copilot MCP 工具使用指南

## 🎉 配置完成！

您的 GitHub Copilot 现在已经配置好了 Augment Agent 的 16 个强大工具！

## 🔧 已配置的工具

### 文件操作工具 (4个)
- **view_file** - 查看文件和目录内容
- **save_file** - 创建新文件
- **edit_file** - 编辑现有文件
- **remove_files** - 删除文件

### 进程管理工具 (5个)
- **launch_process** - 启动进程
- **read_process** - 读取进程输出
- **write_process** - 向进程写入
- **kill_process** - 终止进程
- **list_processes** - 列出进程

### Web 工具 (3个)
- **web_search** - 网络搜索
- **web_fetch** - 获取网页内容
- **open_browser** - 打开浏览器

### 开发工具 (3个)
- **get_diagnostics** - 获取诊断信息
- **read_terminal** - 读取终端输出
- **codebase_search** - 搜索代码库

### 记忆工具 (1个)
- **remember** - 存储信息

## 🚀 如何使用

### 1. 重启 VS Code
首先重启 VS Code 以加载新配置：
```bash
# 完全退出 VS Code，然后重新打开
```

### 2. 在 Copilot Chat 中使用工具

#### 文件操作示例
```
@workspace 查看当前项目的文件结构

@workspace 创建一个新的 Python 文件 hello.py，包含一个简单的 hello world 函数

@workspace 编辑 server.py 文件，在第 50 行添加一个新的函数

@workspace 删除临时文件 temp.txt
```

#### 进程管理示例
```
@workspace 运行 npm install 命令并等待完成

@workspace 启动开发服务器在后台运行

@workspace 检查正在运行的进程状态

@workspace 运行测试命令并显示结果
```

#### Web 工具示例
```
@workspace 获取 https://api.github.com/user 的内容

@workspace 在浏览器中打开项目文档

@workspace 搜索关于 FastMCP 的信息
```

#### 开发工具示例
```
@workspace 搜索代码库中的 TODO 注释

@workspace 获取当前项目的诊断信息

@workspace 读取终端的最新输出
```

#### 记忆功能示例
```
@workspace 记住这个项目使用 FastMCP 框架实现 MCP 服务器

@workspace 记住下次发布前需要更新文档
```

### 3. 在 Cline 扩展中使用

如果您安装了 Cline 扩展，也可以在 Cline 界面中使用这些工具：

1. 打开 Cline 面板
2. 查看可用的 MCP 工具
3. 直接使用工具或通过对话调用

## 🔍 验证工具是否工作

### 方法 1: 运行验证脚本
```bash
cd /Users/<USER>/Documents/augment-projects/mcp
python verify_copilot_mcp_config.py
```

### 方法 2: 在 Copilot Chat 中测试
```
@workspace 使用 view_file 工具查看当前目录
```

### 方法 3: 检查工具列表
在 VS Code 中：
1. 打开 Copilot Chat
2. 查看是否有工具图标 🔧
3. 点击查看可用工具列表

## 🛠️ 故障排除

### 如果工具没有显示

1. **重启 VS Code**
   ```bash
   # 完全退出并重新启动 VS Code
   ```

2. **检查配置**
   ```bash
   python verify_copilot_mcp_config.py
   ```

3. **查看 VS Code 输出**
   - 打开 VS Code 输出面板
   - 选择 "GitHub Copilot" 或 "MCP" 相关的输出
   - 查看错误信息

4. **检查命令行工具**
   ```bash
   which augment-agent-mcp
   augment-agent-mcp --help
   ```

### 如果工具调用失败

1. **检查权限**
   - 确保对文件和目录有适当权限
   - 检查进程执行权限

2. **查看错误信息**
   - Copilot 会显示工具执行的错误信息
   - 根据错误信息调整使用方式

3. **测试单个工具**
   ```bash
   cd /Users/<USER>/Documents/augment-projects/mcp
   python demo.py
   ```

## 📝 最佳实践

### 1. 明确的指令
```
✅ 好的指令: "@workspace 使用 view_file 查看 src 目录的内容"
❌ 模糊指令: "@workspace 看看文件"
```

### 2. 指定工具
```
✅ 明确工具: "@workspace 使用 launch_process 运行 npm test"
❌ 不明确: "@workspace 运行测试"
```

### 3. 提供上下文
```
✅ 有上下文: "@workspace 在项目根目录创建一个新的配置文件 config.json"
❌ 缺上下文: "@workspace 创建配置文件"
```

## 🎯 高级用法

### 组合使用多个工具
```
@workspace 首先查看项目结构，然后创建一个新的测试文件，最后运行测试
```

### 工作流自动化
```
@workspace 执行完整的构建流程：安装依赖、运行测试、生成文档
```

### 代码分析
```
@workspace 分析代码库中的所有 Python 文件，找出需要重构的部分
```

## 📞 获取帮助

如果遇到问题：

1. **查看日志**
   ```bash
   python verify_copilot_mcp_config.py
   ```

2. **重新配置**
   ```bash
   # 恢复备份的设置
   cp vscode_settings_backup.json "/Users/<USER>/Library/Application Support/Code/User/settings.json"
   
   # 重新运行配置
   python verify_copilot_mcp_config.py
   ```

3. **测试基础功能**
   ```bash
   python simple_test.py
   python demo.py
   ```

现在您可以在 GitHub Copilot 中享受 Augment Agent 的强大工具了！🚀
