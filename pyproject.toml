[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "augment-agent-mcp-server"
version = "0.1.0"
description = "MCP server exposing Augment Agent tools for use with Copilot and other MCP clients"
authors = [
    {name = "Augment Agent", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "mcp>=1.2.0",
    "httpx>=0.25.0",
    "aiofiles>=23.0.0",
    "psutil>=5.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
]

[project.scripts]
augment-agent-mcp = "augment_agent_mcp.server:main"

[tool.hatch.build.targets.wheel]
packages = ["augment_agent_mcp"]

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
