#!/usr/bin/env python3
"""Simple test to verify the MCP server works."""

import asyncio
from augment_agent_mcp.server import mcp

async def test_tools():
    """Test that tools are properly registered."""
    print("Testing tool registration...")

    # Check FastMCP attributes
    print(f"FastMCP attributes: {dir(mcp)}")

    # Try to access tools through the tool manager
    if hasattr(mcp, '_tool_manager'):
        tool_manager = mcp._tool_manager
        print(f"Tool manager has _tools: {hasattr(tool_manager, '_tools')}")

        if hasattr(tool_manager, '_tools'):
            tools_dict = tool_manager._tools
            print(f"Found {len(tools_dict)} registered tools:")
            for name in tools_dict.keys():
                print(f"  - {name}")
            return len(tools_dict) > 0
        else:
            print("Tool manager has no _tools attribute")
            return False
    else:
        print("Could not access tool manager")
        return False

def main():
    """Run the test."""
    print("=" * 50)
    print("Simple MCP Server Test")
    print("=" * 50)

    success = asyncio.run(test_tools())

    if success:
        print("\n✓ Test passed! Tools are properly registered.")
        return 0
    else:
        print("\n✗ Test failed! No tools found.")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
